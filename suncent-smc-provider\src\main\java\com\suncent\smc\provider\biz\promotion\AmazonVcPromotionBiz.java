package com.suncent.smc.provider.biz.promotion;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsinStatistics;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealStatistics;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinStatisticsService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealStatisticsService;
import com.suncent.smc.provider.biz.promotion.dto.AmazonVcPromotionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 亚马逊VC促销活动业务处理类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class AmazonVcPromotionBiz {

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmBestDealAsinService amBestDealAsinService;

    @Autowired
    private IAmBestDealStatisticsService amBestDealStatisticsService;

    @Autowired
    private IAmBestDealAsinStatisticsService amBestDealAsinStatisticsService;

    /**
     * 根据promotion_id构建亚马逊VC促销活动JSON
     *
     * @param promotionId 促销ID
     * @return 促销活动JSON字符串
     */
    public String buildPromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 构建促销活动JSON
        AmazonVcPromotionDTO promotionDTO = buildPromotionDTO(promotionRecord, asinList);

        // 4. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(promotionDTO);

        log.info("成功构建促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }

    /**
     * 构建促销活动DTO对象
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 促销活动DTO
     */
    private AmazonVcPromotionDTO buildPromotionDTO(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        AmazonVcPromotionDTO dto = new AmazonVcPromotionDTO();

        // 创建促销活动列表
        List<AmazonVcPromotionDTO.Promotion> promotions = new ArrayList<>();
        AmazonVcPromotionDTO.Promotion promotion = buildPromotion(record, asinList);
        promotions.add(promotion);

        dto.setPromotions(promotions);

        return dto;
    }

    /**
     * 构建单个促销活动对象
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 促销活动对象
     */
    private AmazonVcPromotionDTO.Promotion buildPromotion(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        AmazonVcPromotionDTO.Promotion promotion = new AmazonVcPromotionDTO.Promotion();

        // 构建latest和published版本（通常相同）
        AmazonVcPromotionDTO.PromotionDetail promotionDetail = buildPromotionDetail(record, asinList);
        promotion.setLatest(promotionDetail);
        promotion.setPublished(promotionDetail);

        // 设置基本信息
        promotion.setErrorCount(0);
        promotion.setAsinCount(asinList.size());

        // 设置行动项目
        List<String> actionItems = new ArrayList<>();
        actionItems.add("IS_VIEWABLE");
        promotion.setActionItems(actionItems);

        // 设置UI状态
        promotion.setUiStatus(mapStatusToUiStatus(record.getStatus()));

        // 设置性能数据
        promotion.setPerformance(buildPerformance());

        return promotion;
    }

    /**
     * 构建促销详情对象
     *
     * @param record   促销记录
     * @param asinList ASIN列表
     * @return 促销详情
     */
    private AmazonVcPromotionDTO.PromotionDetail buildPromotionDetail(AmBestDealRecord record, List<AmBestDealAsin> asinList) {
        AmazonVcPromotionDTO.PromotionDetail detail = new AmazonVcPromotionDTO.PromotionDetail();

        // 设置聚合数据
        detail.setAggregates(buildAggregates(asinList));

        // 设置协议信息
        detail.setAgreements(buildAgreements(record));

        // 设置基本信息
        detail.setDisplayName(record.getPromotionName());
        detail.setEgressUrl(buildEgressUrl(record.getPromotionId()));
        detail.setInternalDescription(buildInternalDescription(record));
        detail.setMarketplaceId(getMarketplaceId(record.getSite()));
        detail.setOfferingName("BestDeal");
        detail.setOfferingDisplayName("BestDeal");
        detail.setPromotionId(record.getPromotionId());

        // 设置版本信息
        detail.setRevision(buildRevision(record));

        // 设置优惠信息（空对象）
        detail.setBenefit(new HashMap<>());

        // 设置图片和特色ASIN
        if (!asinList.isEmpty()) {
            AmBestDealAsin firstAsin = asinList.get(0);
            detail.setImageUrl(buildImageUrl(firstAsin.getPlatformGoodsId()));
            detail.setFeaturedAsin(firstAsin.getPlatformGoodsId());
        }

        // 设置时间安排
        detail.setSchedule(buildSchedule(record));

        // 设置产品选择ID
        detail.setProductSelectionId(generateProductSelectionId());

        // 设置错误数量
        detail.setErrorCount(0);

        // 设置促销性能
        detail.setPromotionPerformance(buildPromotionPerformance(record.getPromotionId()));

        // 设置所有者信息
        detail.setOwner(buildOwner(record));

        return detail;
    }

    /**
     * 验证促销活动数据完整性
     *
     * @param promotionId 促销ID
     * @return 验证结果
     */
    public AjaxResult validatePromotionData(String promotionId) {
        try {
            if (StrUtil.isBlank(promotionId)) {
                return AjaxResult.error("促销ID不能为空");
            }

            AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
            if (record == null) {
                return AjaxResult.error("未找到促销记录");
            }

            List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(record.getId());
            if (CollUtil.isEmpty(asinList)) {
                return AjaxResult.error("未找到关联的ASIN记录");
            }

            // 验证必要字段
            List<String> errors = new ArrayList<>();

            if (StrUtil.isBlank(record.getPromotionName())) {
                errors.add("促销活动名称不能为空");
            }

            if (StrUtil.isBlank(record.getStartDateUtc())) {
                errors.add("开始时间不能为空");
            }

            if (StrUtil.isBlank(record.getEndDateUtc())) {
                errors.add("结束时间不能为空");
            }

            // 验证ASIN数据
            for (int i = 0; i < asinList.size(); i++) {
                AmBestDealAsin asin = asinList.get(i);
                if (StrUtil.isBlank(asin.getPlatformGoodsId())) {
                    errors.add("第" + (i + 1) + "个ASIN不能为空");
                }
                if (asin.getStandardPrice() == null || asin.getStandardPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    errors.add("第" + (i + 1) + "个ASIN的标准价格必须大于0");
                }
            }

            if (!errors.isEmpty()) {
                return AjaxResult.error("数据验证失败: " + String.join(", ", errors));
            }

            Map<String, Object> result = new HashMap<>();
            result.put("promotionId", promotionId);
            result.put("promotionName", record.getPromotionName());
            result.put("asinCount", asinList.size());
            result.put("status", record.getStatus());

            return AjaxResult.success("数据验证通过", result);

        } catch (Exception e) {
            log.error("验证促销活动数据失败，促销ID: {}", promotionId, e);
            return AjaxResult.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取促销活动摘要信息
     *
     * @param promotionId 促销ID
     * @return 摘要信息
     */
    public Map<String, Object> getPromotionSummary(String promotionId) {
        Map<String, Object> summary = new HashMap<>();

        try {
            AmBestDealRecord record = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
            if (record == null) {
                summary.put("error", "未找到促销记录");
                return summary;
            }

            List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(record.getId());

            summary.put("promotionId", record.getPromotionId());
            summary.put("promotionName", record.getPromotionName());
            summary.put("status", record.getStatus());
            summary.put("dealType", record.getDealType());
            summary.put("eventType", record.getEventType());
            summary.put("publishType", record.getPublishType());
            summary.put("site", record.getSite());
            summary.put("startDateUtc", record.getStartDateUtc());
            summary.put("endDateUtc", record.getEndDateUtc());
            summary.put("asinCount", asinList != null ? asinList.size() : 0);

            if (CollUtil.isNotEmpty(asinList)) {
                int totalCommittedUnits = asinList.stream()
                        .mapToInt(asin -> asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0)
                        .sum();
                summary.put("totalCommittedUnits", totalCommittedUnits);

                BigDecimal totalDealValue = asinList.stream()
                        .filter(asin -> asin.getDealPrice() != null && asin.getCommittedUnits() != null)
                        .map(asin -> asin.getDealPrice().multiply(new BigDecimal(asin.getCommittedUnits())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                summary.put("totalDealValue", totalDealValue);
            }

        } catch (Exception e) {
            log.error("获取促销活动摘要失败，促销ID: {}", promotionId, e);
            summary.put("error", "获取摘要失败: " + e.getMessage());
        }

        return summary;
    }

    /**
     * 根据promotion_id构建简化的促销活动JSON（用于API提交）
     *
     * @param promotionId 促销ID
     * @return 简化的促销活动JSON字符串
     */
    public String buildSimplePromotionJson(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            throw new BusinessException("促销ID不能为空");
        }

        // 1. 查询促销记录
        AmBestDealRecord promotionRecord = amBestDealRecordService.selectAmBestDealRecordByPromotionId(promotionId);
        if (promotionRecord == null) {
            throw new BusinessException("未找到促销记录，促销ID: " + promotionId);
        }

        // 2. 查询关联的ASIN列表
        List<AmBestDealAsin> asinList = amBestDealAsinService.selectAmBestDealAsinByRefId(promotionRecord.getId());
        if (CollUtil.isEmpty(asinList)) {
            throw new BusinessException("未找到关联的ASIN记录，促销ID: " + promotionId);
        }

        // 3. 查询统计数据（可选）
        AmBestDealStatistics statistics = amBestDealStatisticsService.selectAmBestDealStatisticsByPromotionId(promotionId);
        List<AmBestDealAsinStatistics> asinStatisticsList = amBestDealAsinStatisticsService.selectAmBestDealAsinStatisticsByPromotionId(promotionId);

        // 4. 构建简化的促销活动JSON
        Map<String, Object> result = buildSimplePromotionData(promotionRecord, asinList, statistics, asinStatisticsList);

        // 5. 转换为JSON字符串
        String jsonResult = JSONUtil.toJsonPrettyStr(result);

        log.info("成功构建简化促销活动JSON，促销ID: {}, ASIN数量: {}", promotionId, asinList.size());

        return jsonResult;
    }

    /**
     * 构建简化的促销活动数据
     *
     * @param record             促销记录
     * @param asinList           ASIN列表
     * @param statistics         活动统计数据
     * @param asinStatisticsList ASIN统计数据列表
     * @return 简化的促销活动数据
     */
    private Map<String, Object> buildSimplePromotionData(AmBestDealRecord record, List<AmBestDealAsin> asinList,
                                                          AmBestDealStatistics statistics, List<AmBestDealAsinStatistics> asinStatisticsList) {
        Map<String, Object> result = new HashMap<>();

        // 构建products数组
        List<Map<String, Object>> products = new ArrayList<>();
        for (AmBestDealAsin asin : asinList) {
            Map<String, Object> product = new HashMap<>();
            product.put("asin", asin.getPlatformGoodsId());
            product.put("sku", asin.getPlatformGoodsCode() != null ? asin.getPlatformGoodsCode() : asin.getPlatformGoodsId());

            // 促销数量
            Map<String, Object> promotionQuantity = new HashMap<>();
            promotionQuantity.put("units", asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0);
            product.put("promotionQuantity", promotionQuantity);

            // 单位资助金额
            Map<String, Object> perUnitFunding = new HashMap<>();
            Map<String, Object> fundingValue = new HashMap<>();
            fundingValue.put("amount", asin.getPerUnitFunding() != null ? asin.getPerUnitFunding() : BigDecimal.ZERO);
            fundingValue.put("currencyCode", "USD");
            perUnitFunding.put("value", fundingValue);
            product.put("perUnitFunding", perUnitFunding);

            // 促销价格
            Map<String, Object> promotionPrice = new HashMap<>();
            Map<String, Object> priceValue = new HashMap<>();
            priceValue.put("amount", asin.getDealPrice() != null ? asin.getDealPrice() : BigDecimal.ZERO);
            priceValue.put("currencyCode", "USD");
            promotionPrice.put("value", priceValue);
            product.put("promotionPrice", promotionPrice);

            products.add(product);
        }
        result.put("products", products);

        // 构建promotion对象
        Map<String, Object> promotion = new HashMap<>();

        // promotionId - 如果为空则不添加
        if (StrUtil.isNotBlank(record.getPromotionId())) {
            promotion.put("promotionId", record.getPromotionId());
        }

        // 内部描述
        promotion.put("internalDescription", buildInternalDescription(record));

        // 特色ASIN（第一个ASIN）
        if (!asinList.isEmpty()) {
            promotion.put("featuredAsin", asinList.get(0).getPlatformGoodsId());
        }

        // 市场ID
        promotion.put("marketplaceId", getMarketplaceId(record.getSite()));

        // 产品类型
        promotion.put("offeringName", "BEST_DEAL");

        // 时间安排
        Map<String, Object> schedule = new HashMap<>();
        schedule.put("startDate", convertToSimpleTimeFormat(record.getStartDateUtc()));
        schedule.put("endDate", convertToSimpleTimeFormat(record.getEndDateUtc()));
        promotion.put("schedule", schedule);

        // 所有者信息
        Map<String, Object> owner = new HashMap<>();
        owner.put("vendorCode", determineVendorCode(record.getPublishType(), record.getSite()));
        promotion.put("owner", owner);

        result.put("promotion", promotion);

        return result;
    }

    /**
     * 转换为简单时间格式
     */
    private String convertToSimpleTimeFormat(String utcTime) {
        if (StrUtil.isBlank(utcTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        try {
            // 如果已经是ISO格式，直接返回
            if (utcTime.contains("T")) {
                return utcTime.replace("Z", "");
            }

            // 如果是其他格式，尝试转换
            return utcTime;

        } catch (Exception e) {
            log.error("时间格式转换失败: {}", utcTime, e);
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
    }

    /**
     * 构建聚合数据
     */
    private AmazonVcPromotionDTO.Aggregates buildAggregates(List<AmBestDealAsin> asinList) {
        AmazonVcPromotionDTO.Aggregates aggregates = new AmazonVcPromotionDTO.Aggregates();

        // 计算总承诺数量
        int totalCommittedUnits = asinList.stream()
                .mapToInt(asin -> asin.getCommittedUnits() != null ? asin.getCommittedUnits() : 0)
                .sum();
        aggregates.setCommittedUnits(totalCommittedUnits);

        // 构建折扣信息
        AmazonVcPromotionDTO.Discount discount = new AmazonVcPromotionDTO.Discount();
        int minDiscount = asinList.stream()
                .mapToInt(asin -> asin.getLowestDiscount() != null ? asin.getLowestDiscount() : 0)
                .min().orElse(0);
        int maxDiscount = asinList.stream()
                .mapToInt(asin -> asin.getActualDiscount() != null ? asin.getActualDiscount() : 0)
                .max().orElse(0);

        discount.setMin(minDiscount);
        discount.setMax(maxDiscount);
        discount.setType("PERCENT_OFF_RANGE_DISCOUNT");
        aggregates.setDiscount(discount);

        // 构建资金信息
        AmazonVcPromotionDTO.Funding funding = new AmazonVcPromotionDTO.Funding();
        BigDecimal totalFunding = asinList.stream()
                .filter(asin -> asin.getPerUnitFunding() != null && asin.getCommittedUnits() != null)
                .map(asin -> asin.getPerUnitFunding().multiply(new BigDecimal(asin.getCommittedUnits())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        funding.setAmount(totalFunding);
        funding.setCurrencyCode("USD");
        aggregates.setFunding(funding);

        return aggregates;
    }

    /**
     * 构建协议信息
     */
    private AmazonVcPromotionDTO.Agreements buildAgreements(AmBestDealRecord record) {
        AmazonVcPromotionDTO.Agreements agreements = new AmazonVcPromotionDTO.Agreements();

        List<AmazonVcPromotionDTO.FundingAgreement> fundingAgreements = new ArrayList<>();
        AmazonVcPromotionDTO.FundingAgreement fundingAgreement = new AmazonVcPromotionDTO.FundingAgreement();

        // 如果有资金协议ID，使用它；否则生成默认值
        if (record.getFundingAgreementId() != null) {
            fundingAgreement.setAgreementId(new String(record.getFundingAgreementId()));
        } else {
            fundingAgreement.setAgreementId("87791825"); // 默认协议ID
        }

        fundingAgreements.add(fundingAgreement);
        agreements.setFundingAgreements(fundingAgreements);

        return agreements;
    }

    /**
     * 构建版本信息
     */
    private AmazonVcPromotionDTO.Revision buildRevision(AmBestDealRecord record) {
        AmazonVcPromotionDTO.Revision revision = new AmazonVcPromotionDTO.Revision();

        revision.setStatus(mapStatusToRevisionStatus(record.getStatus()));
        revision.setNumber(1); // 默认版本号

        return revision;
    }

    /**
     * 构建时间安排
     */
    private AmazonVcPromotionDTO.Schedule buildSchedule(AmBestDealRecord record) {
        AmazonVcPromotionDTO.Schedule schedule = new AmazonVcPromotionDTO.Schedule();

        // 转换时间格式为亚马逊要求的格式
        schedule.setStartDate(convertToAmazonTimeFormat(record.getStartDateUtc(), record.getSite()));
        schedule.setEndDate(convertToAmazonTimeFormat(record.getEndDateUtc(), record.getSite()));

        return schedule;
    }

    /**
     * 构建性能数据
     */
    private AmazonVcPromotionDTO.Performance buildPerformance() {
        AmazonVcPromotionDTO.Performance performance = new AmazonVcPromotionDTO.Performance();

        performance.setGlanceViews(0);
        performance.setTotalUnitsSold(0);
        performance.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ISO_INSTANT));

        // 销售额
        AmazonVcPromotionDTO.Sales sales = new AmazonVcPromotionDTO.Sales();
        sales.setAmount(BigDecimal.ZERO);
        sales.setCurrencyCode("USD");
        performance.setSales(sales);

        // 已使用预算
        AmazonVcPromotionDTO.BudgetUtilized budgetUtilized = new AmazonVcPromotionDTO.BudgetUtilized();
        budgetUtilized.setAmount(BigDecimal.ZERO);
        budgetUtilized.setCurrencyCode("USD");
        performance.setBudgetUtilized(budgetUtilized);

        return performance;
    }

    /**
     * 构建促销性能
     */
    private AmazonVcPromotionDTO.PromotionPerformance buildPromotionPerformance(String promotionId) {
        AmazonVcPromotionDTO.PromotionPerformance promotionPerformance = new AmazonVcPromotionDTO.PromotionPerformance();

        // 生成短促销ID（用于显示）
        promotionPerformance.setPromotionId(generateShortPromotionId(promotionId));
        promotionPerformance.setPerformance(buildPerformance());

        return promotionPerformance;
    }

    /**
     * 构建所有者信息
     */
    private AmazonVcPromotionDTO.Owner buildOwner(AmBestDealRecord record) {
        AmazonVcPromotionDTO.Owner owner = new AmazonVcPromotionDTO.Owner();

        // 根据刊登类型确定供应商代码
        String vendorCode = determineVendorCode(record.getPublishType(), record.getSite());
        owner.setVendorCode(vendorCode);

        return owner;
    }

    /**
     * 映射状态到UI状态
     */
    private String mapStatusToUiStatus(String status) {
        if (status == null) return "DRAFT";

        switch (status.toUpperCase()) {
            case "DRAFT":
                return "DRAFT";
            case "NEEDS_YOUR_ATTENTION":
                return "NEEDS_ATTENTION";
            case "CANCELED":
                return "CANCELED";
            case "APPROVED":
                return "APPROVED";
            default:
                return "DRAFT";
        }
    }

    /**
     * 映射状态到版本状态
     */
    private String mapStatusToRevisionStatus(String status) {
        if (status == null) return "Draft";

        switch (status.toUpperCase()) {
            case "DRAFT":
                return "Draft";
            case "NEEDS_YOUR_ATTENTION":
                return "NeedsAttention";
            case "CANCELED":
                return "Tombstoned";
            case "APPROVED":
                return "Published";
            default:
                return "Draft";
        }
    }

    /**
     * 构建出口URL
     */
    private String buildEgressUrl(String promotionId) {
        String shortId = generateShortPromotionId(promotionId);
        return "https://www.amazon.com/deals?deal-ids=" + shortId;
    }

    /**
     * 构建内部描述
     */
    private String buildInternalDescription(AmBestDealRecord record) {
        String dateStr = "";
        if (StrUtil.isNotBlank(record.getStartDateUtc())) {
            try {
                // 提取日期部分
                dateStr = record.getStartDateUtc().substring(0, 10).replace("-", " ");
            } catch (Exception e) {
                dateStr = "Date";
            }
        }

        String vendorCode = determineVendorCode(record.getPublishType(), record.getSite());
        return dateStr + " " + vendorCode + " Best Deal";
    }

    /**
     * 获取市场ID
     */
    private String getMarketplaceId(String site) {
        if (site == null) return "ATVPDKIKX0DER";

        switch (site.toUpperCase()) {
            case "US":
                return "ATVPDKIKX0DER";
            case "UK":
                return "A1F83G8C2ARO7P";
            case "DE":
                return "A1PA6795UKMFR9";
            case "MX":
                return "A1AM78C64UM0Y8";
            default:
                return "ATVPDKIKX0DER";
        }
    }

    /**
     * 构建图片URL
     */
    private String buildImageUrl(String asin) {
        if (StrUtil.isBlank(asin)) {
            return "https://m.media-amazon.com/images/I/default.jpg";
        }
        return "https://m.media-amazon.com/images/I/" + asin + ".jpg";
    }

    /**
     * 生成产品选择ID
     */
    private String generateProductSelectionId() {
        return java.util.UUID.randomUUID().toString();
    }

    /**
     * 生成短促销ID
     */
    private String generateShortPromotionId(String promotionId) {
        if (StrUtil.isBlank(promotionId)) {
            return "00000000";
        }

        // 生成8位短ID
        return String.format("%08x", promotionId.hashCode()).substring(0, 8);
    }

    /**
     * 确定供应商代码
     */
    private String determineVendorCode(Integer publishType, String site) {
        if (publishType == null) return "WM741";

        // 根据刊登类型和站点确定供应商代码
        if (publishType == 5) { // VCDF
            return "WM741";
        } else if (publishType == 6) { // VCPO
            return "IH75B";
        }

        return "WM741"; // 默认值
    }

    /**
     * 转换为亚马逊时间格式
     */
    private String convertToAmazonTimeFormat(String utcTime, String site) {
        if (StrUtil.isBlank(utcTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "-07:00[US/Pacific]";
        }

        try {
            // 如果已经是正确格式，直接返回
            if (utcTime.contains("[")) {
                return utcTime;
            }

            // 转换UTC时间为亚马逊格式
            String timeZone = getTimeZone(site);
            if (utcTime.endsWith("Z")) {
                utcTime = utcTime.substring(0, utcTime.length() - 1);
            }

            return utcTime + timeZone;

        } catch (Exception e) {
            log.error("时间格式转换失败: {}", utcTime, e);
            return LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "-07:00[US/Pacific]";
        }
    }

    /**
     * 获取时区
     */
    private String getTimeZone(String site) {
        if (site == null) return "-07:00[US/Pacific]";

        switch (site.toUpperCase()) {
            case "US":
                return "-07:00[US/Pacific]";
            case "UK":
                return "+00:00[Europe/London]";
            case "DE":
                return "+01:00[Europe/Berlin]";
            case "MX":
                return "-06:00[America/Mexico_City]";
            default:
                return "-07:00[US/Pacific]";
        }
    }
}
