# AmazonVcPromotionBiz 简化JSON构建方法使用说明

## 概述

在 `AmazonVcPromotionBiz` 类中新增了 `buildSimplePromotionJson` 方法，用于构建符合您指定格式的简化促销活动JSON。

## 新增方法

### buildSimplePromotionJson(String promotionId)

根据 promotion_id 构建简化的促销活动JSON，格式符合您的要求。

**参数：**
- `promotionId`: 促销ID（字符串）

**返回值：**
- 简化的促销活动JSON字符串

**异常：**
- `BusinessException`: 当促销ID为空、未找到促销记录或未找到关联ASIN记录时抛出

## 数据来源

该方法从以下数据库表获取数据：

1. **am_best_deal_record** - 促销记录主表
2. **am_best_deal_asin** - 促销ASIN详情表
3. **am_best_deal_statistics** - 促销活动统计数据（可选）
4. **am_best_deal_asin_statistics** - ASIN级别统计数据（可选）

## 生成的JSON格式

```json
{
    "products": [
        {
            "asin": "B0CYBV89NM",
            "sku": "B0CYBV89NM",
            "promotionQuantity": {
                "units": 100
            },
            "perUnitFunding": {
                "value": {
                    "amount": 13.16,
                    "currencyCode": "USD"
                }
            },
            "promotionPrice": {
                "value": {
                    "amount": 83.57,
                    "currencyCode": "USD"
                }
            }
        }
    ],
    "promotion": {
        "promotionId": "52a6a127-5999-43a7-b664-fa8e5da9a0c5",
        "internalDescription": "Aug 1, 2025 IH75B Best Deal",
        "featuredAsin": "B0CYBV89NM",
        "marketplaceId": "ATVPDKIKX0DER",
        "offeringName": "BEST_DEAL",
        "schedule": {
            "startDate": "2025-08-01T00:00:00",
            "endDate": "2025-08-02T23:45:00"
        },
        "owner": {
            "vendorCode": "IH75B"
        }
    }
}
```

## 字段映射说明

### Products 数组字段映射

| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| asin | AmBestDealAsin.platformGoodsId | ASIN |
| sku | AmBestDealAsin.platformGoodsCode | SKU，如果为空则使用ASIN |
| promotionQuantity.units | AmBestDealAsin.committedUnits | 承诺数量 |
| perUnitFunding.value.amount | AmBestDealAsin.perUnitFunding | 单位资助金额 |
| promotionPrice.value.amount | AmBestDealAsin.dealPrice | 促销价格 |

### Promotion 对象字段映射

| JSON字段 | 数据库字段 | 说明 |
|---------|-----------|------|
| promotionId | AmBestDealRecord.promotionId | 促销ID（如果为空则不包含此字段）|
| internalDescription | 计算生成 | 格式：日期 + 供应商代码 + "Best Deal" |
| featuredAsin | 第一个ASIN | 特色ASIN |
| marketplaceId | 根据站点映射 | 市场ID |
| offeringName | 固定值 | "BEST_DEAL" |
| schedule.startDate | AmBestDealRecord.startDateUtc | 开始时间 |
| schedule.endDate | AmBestDealRecord.endDateUtc | 结束时间 |
| owner.vendorCode | 根据刊登类型计算 | 供应商代码 |

## 使用示例

```java
@Autowired
private AmazonVcPromotionBiz amazonVcPromotionBiz;

public void example() {
    try {
        String promotionId = "52a6a127-5999-43a7-b664-fa8e5da9a0c5";
        String jsonResult = amazonVcPromotionBiz.buildSimplePromotionJson(promotionId);
        
        System.out.println("生成的JSON:");
        System.out.println(jsonResult);
        
    } catch (BusinessException e) {
        System.err.println("构建JSON失败: " + e.getMessage());
    }
}
```

## 特殊处理

1. **promotionId为空**: 如果数据库中的promotionId为空，则在生成的JSON中不包含promotionId字段
2. **货币代码**: 目前固定为"USD"
3. **供应商代码**: 根据刊登类型自动确定（VCDF=WM741, VCPO=IH75B）
4. **市场ID**: 根据站点自动映射（US=ATVPDKIKX0DER等）
5. **时间格式**: 自动转换为ISO格式

## 测试

已在 `AmazonVcPromotionBizTest` 中添加了相应的测试方法：
- `testBuildSimplePromotionJson()`: 使用测试数据验证功能
- `testBuildSimplePromotionJsonWithExistingData()`: 使用现有数据验证功能
