package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.Striped;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.SpelUtil;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.ads.mapper.AdsFitmentDiffMapper;
import com.suncent.smc.persistence.amazon.domain.AmazonWarehouseMapping;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import com.suncent.smc.persistence.amazon.service.IAmazonWarehouseMappingService;
import com.suncent.smc.persistence.amazon.service.IVcListingInventoryService;
import com.suncent.smc.persistence.bi.domain.entity.OdsCrlCrlVcCatalogData;
import com.suncent.smc.persistence.bi.domain.entity.ReviewStar;
import com.suncent.smc.persistence.bi.domain.entity.VcInventorySnapshot;
import com.suncent.smc.persistence.bi.service.IOdsCrlCrlVcCatalogDataService;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.impl.ShopServiceImpl;
import com.suncent.smc.persistence.common.domain.LogRecord;
import com.suncent.smc.persistence.common.service.ILogRecordService;
import com.suncent.smc.persistence.configuration.category.service.IGoodsCategoryMappingService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.error.parser.ConfigurableErrorParser;
import com.suncent.smc.persistence.inventory.domain.VcInventoryRecord;
import com.suncent.smc.persistence.inventory.dto.GetFbmStockInvRequestDTO;
import com.suncent.smc.persistence.inventory.service.IVcInventoryRecordService;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.product.domain.entity.ProductDocumentRecord;
import com.suncent.smc.persistence.product.service.ProductDocumentRecordService;
import com.suncent.smc.persistence.publication.domain.dto.AmazonJSONParamDTO;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemBackUpDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.todo.domain.entity.AmListingDeleteMonitoringTodo;
import com.suncent.smc.persistence.todo.domain.entity.AmMonitorPoolConfig;
import com.suncent.smc.persistence.todo.service.IAmListingDeleteMonitoringTodoService;
import com.suncent.smc.persistence.todo.service.IAmMonitorPoolConfigService;
import com.suncent.smc.provider.biz.configuration.CategoryInfoHandleBiz;
import com.suncent.smc.provider.biz.consumer.AmazonListingResultHandler;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.publication.dto.AmazonPushDTO;
import com.suncent.smc.provider.biz.publication.dto.VcDfConfirmTransactionStatus;
import com.suncent.smc.provider.biz.publication.service.IBaseListingService;
import com.suncent.smc.provider.biz.publication.util.AttributeGroupIdGenerator;
import com.suncent.smc.provider.biz.publication.util.AttributeWithGroupInfo;
import com.suncent.smc.provider.inventory.InventoryUpdateComposite;
import com.suncent.smc.provider.inventory.InventoryUpdateResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.suncent.smc.common.enums.AmazonAttributeEnum.EXTERNAL_PRODUCT_ID_TYPE;
import static com.suncent.smc.common.enums.PlatformTypeEnum.AM;

@Service
@Slf4j
public class AmazonProductBiz {

    @Autowired
    protected IGoodsHeadService goodsHeadService;
    @Autowired
    protected IListingLogService listingLogService;
    @Autowired
    protected IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Autowired
    private IAmCategoryTemplateFieldPropService amCategoryTemplateFieldPropService;
    @Autowired
    protected IGoodsResourceService goodsResourceService;
    @Autowired
    protected IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    protected IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    protected IPlatformCategoryService platformCategoryService;
    @Autowired
    private IAmCategoryTemplateFieldService amCategoryTemplateFieldService;
    @Autowired
    private IAmCategoryTemplateSmcMappingService amCategoryTemplateSmcMappingService;
    @Resource
    private ILogRecordService logRecordService;
    @Autowired
    @Lazy
    private ListingUpdateBuilder listingUpdateBuilder;
    @Autowired
    AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected AmazonListingResultHandler amazonListingResultHandler;
    @Autowired
    private IGoodsCategoryMappingService goodsCategoryMappingService;
    @Autowired
    private ImageHandleBiz imageHandleBiz;
    @Autowired
    private IGoodsTaskInfoService goodsTaskInfoService;
    @Autowired
    private  ProductDocumentRecordService productDocumentRecordService;
    @Autowired
    private ShopServiceImpl shopService;
    @Autowired
    @Lazy
    private BaseAmazonProductUpdateV2Task baseAmazonProductUpdateV2Task;
    @Autowired
    @Lazy
    private BaseAmazonProductTask baseAmazonProductTask;
    @Autowired
    private IGoodsHeadBackupService goodsHeadBackupService;
    @Autowired
    IAmazonWarehouseMappingService amazonWarehouseMappingService;
    @Autowired
    IVcListingInventoryService vcListingInventoryService;
    @Autowired
    private IVcInventoryRecordService vcInventoryRecordService;
    @Autowired
    private ThirdpartyInventoryBiz thirdpartyInventoryBiz;
    @Autowired
    private IAmCategoryTemplatePrivateValueService amCategoryTemplatePrivateValueService;
    @Autowired
    private IOdsCrlCrlVcCatalogDataService odsCrlCrlVcCatalogDataService;
    @Autowired
    private  IAmListingDeleteMonitoringTodoService  amListingDeleteMonitoringTodoService;
    @Autowired
    private PDMHttpRequestBiz pdmHttpRequestBiz;
    private Striped<Lock> spreadLock = Striped.lock(32);
    @Autowired
    private IAmBrandLimitConfigService amBrandLimitConfigService;
    @Autowired
    @Qualifier("inventoryUpdateComposite")
    private InventoryUpdateComposite invoiceUpdateComposite;
    @Autowired
    private IConfigStoreInfoService configStoreInfoService;
    @Autowired
    private IGoodsTaskService goodsTaskService;
    @Autowired
    private PlatformListingFactory platformListingFactory;
    // 亚马逊推送失败重试次数
    private static final int AMAZON_PUSH_RETRY_COUNT = 3;
    // 亚马逊推送链接次数记录
    private static final Map<Integer, Integer> AMAZON_PUSH_LINK_COUNT_MAP = new HashMap<>();
    @Autowired
    private AdsFitmentDiffMapper adsFitmentDiffMapper;
    @Autowired
    private ConfigurableErrorParser configurableErrorParser;
    @Autowired
    private IAmShopLimitConfigService amShopLimitConfigService;
    @Autowired
    private ThreadPoolForMonitorManager threadPoolForMonitorManager;
    @Resource
    ISysConfigService sysConfigService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private IAmMonitorPoolConfigService amMonitorPoolConfigService;
    @Autowired
    private IListingAdapterLogService listingAdapterLogService;
    @Autowired
    protected CategoryInfoHandleBiz categoryInfoHandleBiz;

    public void errorLog(List<GoodsHead> goodsHeadVOS, String operationType, String e) {
        for (GoodsHead goodsHead : goodsHeadVOS) {
            insertListingLog(operationType, e, goodsHead);
        }
    }

    public void successLog(List<GoodsHead> goodsHeadVOS,String operationType) {
        for (GoodsHead goodsHead : goodsHeadVOS) {
            String detail = "平台商品编码为：[\"" + goodsHead.getPlatformGoodsCode() + "\"]成功刊登至平台,返回的平台销售编码：" + goodsHead.getPlatformGoodsId();
            if(Objects.equals(operationType, "更新")){
                detail = "Listing更新至平台成功";
            }
            String operName = StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(), "-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy();
            listingLogService.insertSuccessListingLog(detail, operName, goodsHead.getId());
        }
    }

    public void insertListingLog(String operationType, String errorMsg, GoodsHead goodsHead) {
        String parseErrorMsg = configurableErrorParser.parseErrorMsg(errorMsg);
        if (StrUtil.isNotBlank(parseErrorMsg)) {
            errorMsg = parseErrorMsg;
        }
        String detail = operationType + "平台商品编码为：[\"" + goodsHead.getPlatformGoodsCode() + "\"]到Amazon平台";
        String operName = StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(), "-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy();
        listingLogService.insertErrorListingLog(detail, operName, goodsHead.getId(), errorMsg);

        GoodsHead failGoods = new GoodsHead().setId(goodsHead.getId());
        if (Objects.equals(operationType, "下架")) {
            failGoods.setPublishStatus(PublishStatus.OFF_SHELF_FAIL.getType());
        } else if (Objects.equals(operationType, "更新")) {
            failGoods.setPublishStatus(PublishStatus.UPDATING_FAIL.getType());
        } else if (Objects.equals(operationType, "删除")) {
            failGoods.setPublishStatus(PublishStatus.DELETE_FAIL.getType());
        } else {
            failGoods.setPublishStatus(PublishStatus.PUBLISH_FAIL.getType());
        }
        failGoods.setPublishingHandler("已处理");
        goodsHeadService.updateListingGoodsHead(failGoods);

        List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
        taskTypeEnum.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_EDIT);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_DELISTING);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_DELETE);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        taskTypeEnum.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        taskTypeEnum.add(GoodsTaskTypeEnum.ONE_KEY_FOLLOW);
        taskTypeEnum.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
        taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);

        goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), taskTypeEnum, GoodsTaskSubStatusEnum.ERROR, errorMsg);
    }

    public void uploadGoodsHeads(AmazonPushDTO amazonPushDTO) {
        // 校验必须参数
        checkRequired(amazonPushDTO);

        String shopCode = amazonPushDTO.getShopCode();
        String operationType = amazonPushDTO.getOperationType();
        List<GoodsTaskTypeEnum> goodsTaskTypes = amazonPushDTO.getGoodsTaskTypes();
        PlatformCategory categoryInfo = amazonPushDTO.getCategoryInfo();

        boolean isAdd = OperationTypeEnum.PUBLISH.getName().equalsIgnoreCase(operationType);

        List<GoodsHead> goodsHeads = amazonPushDTO.getGoodsHeadVOS();
        for (GoodsHead goodsHead : goodsHeads) {
            // 查询最新的数据
            GoodsHead dbGoodsHead = goodsHeadService.selectListingGoodsHeadById(goodsHead.getId());
            // 检查状态是否已经改变
            if (dbGoodsHead == null || !Objects.equals(dbGoodsHead.getPublishStatus(), goodsHead.getPublishStatus())) {
                log.error("商品状态已变更，跳过处理，headId：{}", goodsHead.getId());
                continue;
            }
            
            String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y" : "N";

            final String logKey = String.format("headId：%s, goodsCode:%s, shopCode: %s, 新建刊登：%s, 操作类型：%s", goodsHead.getId(), goodsHead.getPdmGoodsCode(), shopCode, isAdd, operationType);
            final String redisKey = String.format("amazon:v2:upload:%s:%s", goodsHead.getId(), shopCode);
            final String processTimeKey = String.format("amazon:v2:processTime:%s", goodsHead.getId());

            RLock lock = redissonClient.getLock(redisKey);
            boolean locked;
            try {
                // 尝试获取锁,第一个参数是最大等待时间，第二个参数是持有锁的时间，设置为-1表示自动续期
                locked = lock.tryLock(10, -1, TimeUnit.SECONDS);
            }
            // 捕获中断异常，一般是服务重启导致的
            catch (InterruptedException e) {
                log.error("Interrupted while trying to acquire lock for key: {}", redisKey, e);
                continue;
            }

            try {
                if (!locked) {
                    log.error("{}获取锁失败", logKey);
                    continue;
                }

                // 校验操作类型和数据是否匹配
                boolean checkResult = checkOperationTypeAndData(operationType, goodsHead, logKey);
                if (!checkResult) {
                    log.error("{}校验失败", logKey);
                    continue;
                }

                log.info("{}推送开始", logKey);

                Map<String, Object> amazonJsonFieldMap = null;
                if (Objects.equals(operationType, "跟卖")) {
                    //跟卖
                    amazonJsonFieldMap = getAmazonJsonFieldMap(goodsHead.getId(), categoryInfo.getId().intValue(), vcFlag, null, Constants.YesOrNo.NO);

                    //将 external_product_id 移除 添加到 merchant_suggested_asin这个属性
                    followAsin(amazonJsonFieldMap, goodsHead.getId());
                }else if (isAdd) {
                    // 新增，构建全量属性的json map
                    amazonJsonFieldMap = getAmazonJsonFieldMap(goodsHead.getId(), categoryInfo.getId().intValue(), vcFlag, null, Constants.YesOrNo.NO);
                    addAsin(amazonJsonFieldMap, vcFlag);
                } else if (Objects.equals(operationType, "更新")){
                    // 部分更新
                    Boolean isContinue = comparativeData(goodsHead);
                    if (isContinue) {
                        continue;
                    }
                    //全量更新
                    amazonJsonFieldMap = getAmazonJsonFieldMap(goodsHead.getId(), categoryInfo.getId().intValue(),vcFlag, null, Constants.YesOrNo.NO);
                    if ("Y".equals(vcFlag)) {
                        //过滤不可编辑字段
                        amazonJsonFieldMap = filterNoEditField(amazonJsonFieldMap, categoryInfo, vcFlag);
                    } else {
                        addAsin(amazonJsonFieldMap, vcFlag);
                    }
                }
                if (ObjectUtils.isEmpty(amazonJsonFieldMap)) {
                    log.error("{}推送失败，获取Amazon JSON数据失败", logKey);
                    insertListingLog(operationType, "获取Amazon JSON数据失败", goodsHead);
                    continue;
                }

                AmazonListingJSONFeedVO feed = AmazonListingJSONFeedVO.builder()
                        .sku(goodsHead.getPlatformGoodsCode())
                        .productType(categoryInfo.getProductType())
                        .sellerCode(shopCode)
                        .attributes(amazonJsonFieldMap)
                        .build();

                ProductDocumentRecord productDocumentRecord = new ProductDocumentRecord();
                productDocumentRecord.setFileName("UPLOAD_LISTING_"+operationType);
                productDocumentRecord.setListingIds(JSON.toJSONString(Arrays.asList(goodsHead.getId())));
                productDocumentRecord.setCreateTime(new Date());
                productDocumentRecord.setRemark(JSON.toJSONString(feed));
                productDocumentRecordService.insertRecord(productDocumentRecord);

                redisService.setCacheObject(processTimeKey, new Date(), 4L, TimeUnit.HOURS);

                AjaxResult ajaxResult = amazonApiHttpRequestBiz.uploadSkuV2Wapper(goodsHead.getPublishType(), feed);
                log.info("商品ID：{}，商品编码：{}，首次处理时间：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode(), new Date());
                if (!ajaxResult.isSuccess()) {
                    String msg = (String) ajaxResult.get(AjaxResult.MSG_TAG);
                    log.error("{}推送失败，{}", logKey, msg);
                    if (StrUtil.isNotBlank(msg)) {
                        if (msg.contains("java.net.SocketTimeoutException") || msg.contains("Unable to Retrieve Media Content")) {
                            GoodsHead updateGoodsHead = new GoodsHead();
                            updateGoodsHead.setId(goodsHead.getId());
                            updateGoodsHead.setPublishingHandler("处理失败");
                            goodsHeadService.updateListingGoodsHead(updateGoodsHead);
                            Integer count = AMAZON_PUSH_LINK_COUNT_MAP.getOrDefault(goodsHead.getId(), 0);
                            AMAZON_PUSH_LINK_COUNT_MAP.put(goodsHead.getId(), count + 1);
                            // 如果推送失败次数大于等于3次，则记录日志,标记listing为失败
                            if (count >= AMAZON_PUSH_RETRY_COUNT) {
                                insertListingLog(operationType, msg, goodsHead);
                            }
                        } else {
                            insertListingLog(operationType, msg, goodsHead);
                        }
                    } else {
                        insertListingLog(operationType, String.valueOf(ajaxResult.get(AjaxResult.MSG_TAG)), goodsHead);
                    }
                    redisService.deleteObject(processTimeKey);
                    continue;
                }
                // 回写ASIN
                JSONObject data = JSON.parseObject(JSON.toJSONString(ajaxResult.get(AjaxResult.DATA_TAG)));
                String asin = callbackAsin(data, goodsHead,operationType);
                if(StrUtil.isBlank(asin)){
                    log.info("{}推送结束", logKey);
                    continue;
                }
                if (Objects.equals(operationType, "跟卖")) {
                    callbackPublish(goodsHead);
                }

                if (isAdd && StrUtil.isNotBlank(asin)) {
                    callbackPublish(goodsHead);
                }

                log.info("{}推送结束", logKey);
            } catch (Exception e) {
                log.error(logKey + "，推送异常", e);
                redisService.deleteObject(processTimeKey);
                //创建刊登错误日志
                errorLog(Collections.singletonList(goodsHead), operationType, e.getMessage());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            } finally {
                try {
                    if (locked) {
                        lock.unlock();
                    }
                }
                // 其他线程已经释放锁
                catch (IllegalMonitorStateException e) {
                    log.error("Attempted to unlock a lock not held by current thread: {}", redisKey, e);
                }
            }
        }
    }
 
    /**
     * 新增上传参数
     *
     * @param amazonJsonFieldMap
     * @param vcFlag
     */
    private void addAsin(Map<String, Object> amazonJsonFieldMap, String vcFlag) {
        //sc 通过supplier_declared_has_product_identifier_exemption 控制是否传递跟卖asin
        if (Objects.equals(vcFlag, "N")) {
            amazonJsonFieldMap.put("supplier_declared_has_product_identifier_exemption", Lists.newArrayList(new AmazonJSONParamDTO("true")));
            amazonJsonFieldMap.remove("merchant_suggested_asin");
        }
    }

    public void followAsin(Map<String, Object> amazonJsonFieldMap, Integer goodsId) {
        String followAsin = listingAmazonAttributeLineV2Service.getFollowAsin(goodsId);
        if (ObjUtil.isNull(followAsin)) {
            throw new BusinessException("未找到跟卖ASIN,请重新确认.");
        }
        AmazonJSONParamDTO amazonAsinJSONParamDTO = new AmazonJSONParamDTO(followAsin);
        amazonJsonFieldMap.put("merchant_suggested_asin", Lists.newArrayList(amazonAsinJSONParamDTO));
    }

    public String callbackAsin(JSONObject data, GoodsHead goodsHead,  String operationType) {
        if (ObjectUtil.isEmpty(data)) {
            return null;
        }
        JSONArray summaries = data.getJSONArray("summaries");

        if (CollUtil.isEmpty(summaries)) {
            return null;
        }
        JSONObject summary = summaries.getJSONObject(0);
        String asin = summary.getString("asin");
        if (StrUtil.isNotBlank(asin)) {
            goodsHead.setPlatformGoodsId(asin);
            int publishStatus = goodsHead.getShopCode().contains("VC") ? PublishStatus.OFF_SALE.getType() : PublishStatus.SALEING.getType();
            Date onlineTime = goodsHead.getOnlineTime() == null ? new Date() : null;
            // 状态更新成非在售
            GoodsHead updateGoodsHead = new GoodsHead().setId(goodsHead.getId()).setPlatformGoodsId(asin).setPublishStatus(publishStatus).setPublishingHandler("已处理").setOnlineTime(onlineTime);
            goodsHeadService.updateListingGoodsHead(updateGoodsHead);
            try {
                fillCategoryInfoTemplateField(goodsHead);
            }catch (Exception ex) {
                log.error("填充品类模板字段失败", ex);
            }
            goodsHead.setPublishStatus(updateGoodsHead.getPublishStatus());
            goodsHead.setPublishingHandler(updateGoodsHead.getPublishingHandler());
            goodsHead.setOnlineTime(updateGoodsHead.getOnlineTime());
            
            // 更新任务状态
            updateTaskStatus(Collections.singletonList(goodsHead));
            // 插入日志
            successLog(Collections.singletonList(goodsHead),operationType);
            return asin;
        }
        return null;
    }



    public void fillCategoryInfoTemplateField(GoodsHead goodsHead) {
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        if (ObjectUtil.isNull(platformCategory)) {
            return;
        }
        String vcFlag = goodsHead.getShopCode().contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;

        String key = "fillCategoryInfoTemplateField:" + platformCategory.getProductType() + ":" + goodsHead.getSiteCode() + ":" + goodsHead.getCategoryId() + ":" + goodsHead.getShopCode() + ":" + vcFlag + ":" + goodsHead.getCreateBy();
        Lock lock = spreadLock.get(key);
        if (!lock.tryLock()) {
            log.info("当前商品正在填充品类模板字段，goodsId:{}", goodsHead.getId());
            return;
        }
        try {

            // 判断当前创建人在对应品类配置是否有配置私有值，如果没有就填充，如果有就不填充
            int privateValueCount = amCategoryTemplatePrivateValueService.count(goodsHead.getSiteCode(), String.valueOf(goodsHead.getCategoryId()), goodsHead.getShopCode(), platformCategory.getProductType(), vcFlag, goodsHead.getCreateBy());
            if (privateValueCount > 0) {
                return;
            }
            // 将当前链接的属性填充到对应的品类模板字段中
            List<ListingAmazonAttributeLineV2> attributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHead.getId());
            if (CollUtil.isEmpty(attributeLineV2s)) {
                return;
            }
            attributeLineV2s.removeIf(e -> e.getTableType() != null && e.getTableType().equals(5));

            List<String> attributeV2Names = AmazonAttributeEnum.getEnumValuesByFlag(vcFlag);

            attributeLineV2s = attributeLineV2s.stream().filter(attributeLineV2 -> noTemplateField(attributeLineV2, attributeV2Names)).collect(Collectors.toList());
            if (CollUtil.isEmpty(attributeLineV2s)) {
                return;
            }

            List<AmCategoryTemplateField> amCategoryTemplateFields = amCategoryTemplateFieldService.selectAmAllCategoryTemplateFieldList(platformCategory.getProductType(), goodsHead.getSiteCode(), vcFlag);
            List<String> propNodePaths = amCategoryTemplateFields.stream().map(AmCategoryTemplateField::getPropNodePath).distinct().collect(Collectors.toList());
            attributeLineV2s = attributeLineV2s.stream().filter(attributeLineV2 -> propNodePaths.contains(attributeLineV2.getPropNodePath())).collect(Collectors.toList());
            if (CollUtil.isEmpty(attributeLineV2s)) {
                return;
            }
            List<AmCategoryTemplatePrivateValue> privateValues = convertToPrivateValue(attributeLineV2s, goodsHead, platformCategory, vcFlag);
            amCategoryTemplatePrivateValueService.insertBatch(privateValues);
        } finally {
            lock.unlock();
        }
    }

    private List<AmCategoryTemplatePrivateValue> convertToPrivateValue(List<ListingAmazonAttributeLineV2> attributeLineV2s, GoodsHead goodsHead, PlatformCategory platformCategory, String vcFlag) {
        List<AmCategoryTemplatePrivateValue> privateValues = new ArrayList<>();
        for (ListingAmazonAttributeLineV2 attributeLineV2 : attributeLineV2s) {
            AmCategoryTemplatePrivateValue privateValue = new AmCategoryTemplatePrivateValue();
            privateValue.setSite(goodsHead.getSiteCode());
            privateValue.setCategoryId(Long.valueOf(goodsHead.getCategoryId()));
            privateValue.setShopCode(goodsHead.getShopCode());
            privateValue.setProductType(platformCategory.getProductType());
            privateValue.setVcFlag(vcFlag);
            privateValue.setPropNodePath(attributeLineV2.getPropNodePath());
            privateValue.setValue(attributeLineV2.getTableValue());
            privateValue.setCreateBy(goodsHead.getCreateBy());
            privateValue.setCreateTime(new Date());
            privateValues.add(privateValue);
        }
        return privateValues;
    }

    private boolean noTemplateField(ListingAmazonAttributeLineV2 attributeLineV2, List<String> attributeV2Names) {
          return !attributeV2Names.contains(attributeLineV2.getPropNodePath()) && !Arrays.asList("merchant_suggested_asin.value","part_number.value","child_parent_sku_relationship.parent_sku",
                  "child_parent_sku_relationship.child_relationship_type", "size.value", "variation_theme.name", "rtip_items_per_inner_pack.value", "pattern.value", "parentage_level.value",
                  "oe_manufacturer.value").contains(attributeLineV2.getPropNodePath());
    }


    private void callbackPublish(GoodsHead goodsHead) {
        // 回写商品编码与品类的映射
        goodsCategoryMappingService.addNumByPdmGoodsCode(AM.name(), goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode(), String.valueOf(goodsHead.getCategoryId()));
        // 回写ASIN到PDM
        amazonListingResultHandler.listingReportConsumerHandle(JSON.toJSONString(Collections.singletonList(goodsHead)), "publish");
        log.info("回写pdm mapping,店铺:{},商品id:{}完成", goodsHead.getShopCode(), goodsHead.getId());
        //记适配日志
//        listingAdapterLogService.insertSuccessListingLog(goodsHead.getPlatform(),Long.valueOf(goodsHead.getId()), goodsHead.getPlatformGoodsId(), "添加至适配管控池", goodsHead.getCreateBy(), DateUtils.getNowDate());
        //如果是VC-DF还需要进行库存更新
        threadPoolForMonitorManager.getThreadPoolExecutor("amazon-push-scheduled-update").submit(() -> {
            try {
                if (Objects.equals(PublishType.VCDF.getType(), goodsHead.getPublishType())) {
                    InventoryUpdateResolver inventoryUpdateResolver = invoiceUpdateComposite.getInventoryUpdateResolver(goodsHead.getPlatform() + goodsHead.getPublishType());
                    if (ObjectUtils.isEmpty(inventoryUpdateResolver)) {
                        return;
                    }
                    ConfigStoreInfo configStoreInfo = configStoreInfoService.selectConfigStoreInfoByShopCode(goodsHead.getShopCode());
                    if (ObjUtil.isEmpty(configStoreInfo)) {
                        return;
                    }
                    List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsHead.getPdmGoodsCode()), goodsHead.getSiteCode(), true);
                    Map<String, Integer> sellerQtyMap = thirdpartyFbmDTOList.stream()
                            .collect(Collectors.toMap(e -> e.getWhCountry() + e.getWarehouseCode() + e.getSku(),
                                    e -> e.getSellableQty()));
                    //库存校准
                    inventoryUpdateResolver.inventoryCalibration(Collections.singletonList(goodsHead), PlatformTypeEnum.AM.name(), configStoreInfo, sellerQtyMap);
                }
            }catch (Exception e){
                log.error(String.format("VC-DF库存更新异常,商品id:%s", goodsHead.getId()), e);
            }
        });
        // 删除亚马逊推送失败次数记录
        if (AMAZON_PUSH_LINK_COUNT_MAP.containsKey(goodsHead.getId())) {
            AMAZON_PUSH_LINK_COUNT_MAP.remove(goodsHead.getId());
        }
    }

    /**
     * 更新任务状态
     *
     * @param goodsHeadList
     */
    private void updateTaskStatus(List<GoodsHead> goodsHeadList) {
        //更新日志
        for (GoodsHead goodsHead : goodsHeadList) {
            try {
                List<GoodsTaskTypeEnum> taskTypeEnum = new ArrayList<>();
                taskTypeEnum.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_MODIFY);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_EDIT);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_DELISTING);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_DELETE);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_IMPORT);
                taskTypeEnum.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
                taskTypeEnum.add(GoodsTaskTypeEnum.ONE_KEY_FOLLOW);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
                taskTypeEnum.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);

                String publishStatusName = PublishStatus.getPublishStatusName(goodsHead.getPublishStatus());
                Integer publishStatus = goodsHead.getPublishStatus();
//
//                ListingLog listingLog = new ListingLog();
//                listingLog.setDetails("状态: " + publishStatusName + ",商品编码为：[" + goodsHead.getPlatformGoodsCode() + "]到Amazon平台");
//                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) || Objects.equals(goodsHead.getUpdateBy(), "-1") ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
//                listingLog.setOperTime(new Date());
//                listingLog.setListingId(goodsHead.getId());

                //如果 状态是在售或 非在售  则操作成功
                if (Objects.equals(publishStatus, PublishStatus.SALEING.getType())
                        || Objects.equals(publishStatus, PublishStatus.OFF_SALE.getType())
                        || Objects.equals(publishStatus, PublishStatus.DELETE_SUCCESS.getType())) {
//                    listingLog.setStatus(0);

                    goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), taskTypeEnum, GoodsTaskSubStatusEnum.NORAML, "");
//                    listingLogService.insertListingLog(listingLog);
                }
            } catch (Exception e) {
                log.error("操作Amazon商品消费端，更新日志失败", e);
            }
        }
    }

    private Map<String, Object> filterNoEditField(Map<String, Object> amazonJsonFieldMap,PlatformCategory categoryInfo,String vcFlag) {
        if (ObjectUtils.isEmpty(categoryInfo) || StrUtil.isBlank(categoryInfo.getProductType())){
            return amazonJsonFieldMap;
        }

        List<String> noEditFlag = amCategoryTemplateFieldPropService.noEditFlag(categoryInfo.getProductType(), vcFlag, categoryInfo.getSite());
        if ( CollUtil.isEmpty(noEditFlag) ){
            return amazonJsonFieldMap;
        }
        noEditFlag.removeIf(s -> Arrays.asList("externally_assigned_product_identifier", "external_product_id").contains(s));

        Iterator<String> iterator = amazonJsonFieldMap.keySet().iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (noEditFlag.contains(key)){
                iterator.remove();
            }
        }

        return amazonJsonFieldMap;
    }


    /**
     * 对比数据->针对单个详情编辑更新的数据做对比
     *
     * @param goodsHead
     * @return
     */
    private Boolean comparativeData(GoodsHead goodsHead) {
        try {
            //拿到创建时间为最近6h的数据
            LogRecord selectLogRecord = new LogRecord();
            selectLogRecord.setType(OperTypeEnum.SINGLE_EDIT.name());
            selectLogRecord.setGoodsId(String.valueOf(goodsHead.getId()));
            List<LogRecord> logRecords = logRecordService.selectSmcLogRecordList(selectLogRecord);
            if (CollUtil.isEmpty(logRecords)) {
                return false;
            }

            LogRecord logRecord = logRecords
                    .stream()
                    .filter(f -> f.getCreateTime().getTime() > System.currentTimeMillis() - 6 * 60 * 60 * 1000)
                    //拿到最近的一条数据
                    .max(Comparator.comparing(LogRecord::getCreateTime)).orElse(null);
            if (ObjectUtils.isEmpty(logRecord)) {
                return false;
            }
            ItemBackUpDTO itemBackUpDTO = JSON.parseObject(logRecord.getParam(), ItemBackUpDTO.class);
            if (ObjectUtils.isEmpty(itemBackUpDTO)) {
                return false;
            }
            GoodsHead oldHead = itemBackUpDTO.getGoodsHead();
            //更新失败的状态不做对比 直接全量更新
            if (Objects.equals(oldHead.getPublishStatus(), PublishStatus.UPDATING_FAIL.getType())) {
                return false;
            }

            //属性
            List<ListingAmazonAttributeLineV2> attributeLineList = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHead.getId());
            attributeLineList.removeIf(e -> e.getTableType() != null && e.getTableType().equals(5));

            //描述
            GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHead.getId());
            //图片
            List<GoodsResource> resourceList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsHead.getId());
            //规格信息
            GoodsSpecification goodsSpecification = goodsSpecificationService.selectSpecificationListByGoodsId(goodsHead.getId());
            List<String> moduleType = new ArrayList<>();
            List<String> excludeFields = new ArrayList<>();
            excludeFields.add("id");
            excludeFields.add("createTime");
            excludeFields.add("updateTime");
            excludeFields.add("createBy");
            excludeFields.add("updateBy");
            excludeFields.add("params");
            excludeFields.add("remark");

            //对比标题
            if (!Objects.equals(goodsHead.getTitle(), oldHead.getTitle())) {
                moduleType.add(ListingModuleType.TITLE.name());
            }
            //对比价格
//            if (!Objects.equals(goodsHead.getStandardPrice(), oldHead.getStandardPrice())) {
//                moduleType.add(ListingModuleType.PRICE.name());
//            }
            //对比库存
//            if (!Objects.equals(goodsHead.getStockOnSalesQty(), oldHead.getStockOnSalesQty())) {
//                moduleType.add(ListingModuleType.INVENTORY.name());
//            }
            //对比品牌
//            if (!Objects.equals(goodsHead.getBrandCode(), oldHead.getBrandCode())) {
//                moduleType.add(ListingModuleType.BRAND.name());
//            }
            //对比描述
            if (!EqualsBuilder.reflectionEquals(goodsDescription, itemBackUpDTO.getGoodDescription(), excludeFields)) {
                moduleType.add(ListingModuleType.AMAZON_FIVE.name());
                moduleType.add(ListingModuleType.DESCRIPTION.name());
            }
            //对比属性
            if (attributeLineList.size() != itemBackUpDTO.getAttributeLineV2s().size()) {
                moduleType.add(ListingModuleType.ATTRIBUTE.name());
            }else {
                for (int i = 0; i < itemBackUpDTO.getAttributeLineV2s().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(attributeLineList.get(i), itemBackUpDTO.getAttributeLineV2s().get(i), excludeFields)) {
                        moduleType.add(ListingModuleType.ATTRIBUTE.name());
                        break;
                    }
                }
            }
            //对比规格信息
            if (!EqualsBuilder.reflectionEquals(goodsSpecification, itemBackUpDTO.getGoodsSpecification(), excludeFields)) {
                moduleType.add(ListingModuleType.SPECIFICATION.name());
            }
            //对比图片
            if (resourceList.size() != itemBackUpDTO.getGoodsResourceList().size()) {
                moduleType.add(ListingModuleType.IMAGE.name());
            } else {
                for (int i = 0; i < itemBackUpDTO.getGoodsResourceList().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(resourceList.get(i), itemBackUpDTO.getGoodsResourceList().get(i), excludeFields)) {
                        moduleType.add(ListingModuleType.IMAGE.name());
                        break;
                    }
                }
            }
            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodsHead);
            itemDTO.setModuleType(moduleType);

            if (CollUtil.isEmpty(moduleType)){
                return false;
            }
            if (moduleType.contains(ListingModuleType.ATTRIBUTE.name())){
                return false;
            }
            listingUpdateBuilder.updateApi(Lists.newArrayList(itemDTO));
            return true;
        } catch (Exception e) {
            log.error("amazon 更新listing,对比数据异常", e);
            return false;
        }

    }


    private boolean checkOperationTypeAndData(String operationType, GoodsHead goodsHeadVO, String logKey) {
        // 只处理上架、下架、更新、删除、重新刊登
        if (!Arrays.asList("跟卖",
                OperationTypeEnum.PUBLISH.getName(),
                OperationTypeEnum.OFF_SHELF.getName(),
                OperationTypeEnum.UPDATE.getName(),
                OperationTypeEnum.DELETE.getName(),
                OperationTypeEnum.RELIST.getName()).contains(operationType)) {
            log.error("{}操作类型不正确", logKey);
            return false;
        }

        // 上架
        if (Objects.equals(operationType, OperationTypeEnum.PUBLISH.getName())) {
                // 平台商品ID必须为空
                if (StringUtils.isNotBlank(goodsHeadVO.getPlatformGoodsId())) {
                    log.error("{}平台商品ID不为空", logKey);
                    return false;
                }
                // todo 加入状态校验
        }
        // 下架、更新、删除、重新刊登
        if (Arrays.asList(OperationTypeEnum.OFF_SHELF.getName(), OperationTypeEnum.UPDATE.getName(), OperationTypeEnum.DELETE.getName(), OperationTypeEnum.RELIST.getName()).contains(operationType)) {
            // 平台商品ID必须不为空
            if (StringUtils.isBlank(goodsHeadVO.getPlatformGoodsId())) {
                log.error("{}平台商品ID为空", logKey);
              return false;
            }
            // todo 加入状态校验
        }
        return true;
    }

    /**
     * 组装刊登接口的JSON数据
     * 分为两块
     *
     * @param headId           头表ID，可以为空，如果有的话，属性通过头表ID获取，为空的话，属性通过attributeLineV2s获取
     * @param categoryId       品类ID，
     * @param vcFlag           是否VC店铺，Y是，N否
     * @param attributeLineV2s 属性行信息，用于获取指定属性部分的JSON，如果onlyGetJson为Y，此参数不可以为空，如果为N，此参数可为空
     * @param onlyGetJson 是否只获取JSON，Y是，N否，用于获取指定属性的JSON，如果为Y，attributeLineV2s不能为空，默认不自动将映射属性加入到JSON,如果为N，attributeLineV2s可为空，自动将映射属性加入到JSON
     *
     * @param headId
     * @param vcFlag
     * @param attributeLineV2s
     * @return
     */
    public Map<String, Object> getAmazonJsonFieldMap(Integer headId, Integer categoryId, String vcFlag, List<ListingAmazonAttributeLineV2> attributeLineV2s, String onlyGetJson) {
        PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(Long.valueOf(categoryId));
        List<AmCategoryTemplateSmcMapping> templateSmcMappings = null;
        // onlyGetJson为N时，自动将标题、五点、描述的属性加入JSON
        if (Constants.YesOrNo.NO.equals(onlyGetJson)) {
            templateSmcMappings = amCategoryTemplateSmcMappingService.listMapping(null, categoryInfo.getSite(), vcFlag);
        }
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(headId);
        return getAmazonJsonFieldMapV2(goodsHead, categoryId, vcFlag, attributeLineV2s, onlyGetJson, templateSmcMappings);
    }

    /**
     * 组装刊登接口的JSON数据
     *
     * @param goodsHead
     * @param categoryId          品类ID，
     * @param vcFlag              是否VC店铺，Y是，N否
     * @param attributeLineV2s    属性行信息，用于获取指定属性部分的JSON，如果onlyGetJson为Y，此参数不可以为空，如果为N，此参数可为空
     * @param onlyGetJson         是否只获取JSON，Y是，N否，用于获取指定属性的JSON，如果为Y，attributeLineV2s不能为空，如果为N，attributeLineV2s可为空
     * @param templateSmcMappings 品类模板字段映射，用于获取标题、五点、描述的字段映射，如果不传就不参与JSON的组装
     * @return
     */
    public Map<String, Object> getAmazonJsonFieldMapV2(GoodsHead goodsHead, Integer categoryId, String vcFlag, List<ListingAmazonAttributeLineV2> attributeLineV2s,
                                                       String onlyGetJson, List<AmCategoryTemplateSmcMapping> templateSmcMappings){
        if (ObjectUtil.isEmpty(categoryId)) {
            throw new BusinessException("品类ID不能为空");
        }

        PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(Long.valueOf(categoryId));
        if (ObjectUtil.isEmpty(categoryInfo)) {
            throw new BusinessException("品类ID：" + categoryId + "，品类信息不存在");
        }
        Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap = MapUtil.newHashMap();
        List<String> mappingFields = Collections.emptyList();
        if (Constants.YesOrNo.YES.equals(onlyGetJson)) {
            if (CollUtil.isEmpty(attributeLineV2s)) {
                throw new BusinessException("刷新JSON数据，商品属性信息不存在");
            }
            // 对prop_node_path为EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag)的属性进行小写转义
            attributeLineV2s.forEach(e -> {
                if (StrUtil.isNotBlank(e.getPropNodePath()) && e.getPropNodePath().equals(EXTERNAL_PRODUCT_ID_TYPE.getInfoV2(vcFlag)) && StrUtil.isNotBlank(e.getTableValue())) {
                    e.setTableValue(e.getTableValue().toLowerCase());
                }
            });
        } else {
            if (Objects.isNull(goodsHead)) {
                throw new BusinessException("商品ID不能为空");
            }

            attributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHead.getId());
            if (CollUtil.isEmpty(attributeLineV2s)) {
                throw new BusinessException(String.format("商品ID：%s，商品编码：%s，没有属性信息", goodsHead.getId(), goodsHead.getPdmGoodsCode()));
            }
            //移除跟卖特有属性
            attributeLineV2s.removeIf(attributeLineV2 -> attributeLineV2.getTableType() != null && Objects.equals(5, attributeLineV2.getTableType()));

            // 如果propNodePath是external_product_id.type，值改为小写
            attributeLineV2s.forEach(attributeLineV2 -> {
                if (StrUtil.isNotBlank(attributeLineV2.getPropNodePath()) && Arrays.asList(EXTERNAL_PRODUCT_ID_TYPE.getInfoV2VC(), EXTERNAL_PRODUCT_ID_TYPE.getInfoV2SC()).contains(attributeLineV2.getPropNodePath()) && StrUtil.isNotBlank(attributeLineV2.getTableValue())) {
                    attributeLineV2.setTableValue(attributeLineV2.getTableValue().toLowerCase());
                }
            });
        }
        if (CollUtil.isNotEmpty(templateSmcMappings)) {
            // templateSmcMappings to map
            mappingFieldMap = templateSmcMappings.stream().collect(Collectors.groupingBy(AmCategoryTemplateSmcMapping::getPropNodePath));
            mappingFields = mappingFieldMap.values().stream().flatMap(List::stream).map(AmCategoryTemplateSmcMapping::getMappingField).collect(Collectors.toList());
        }

        // group by propNodePath，这个表的这个字段是叶子节点的，需要获取到最上层的节点，组装成JSON
        Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map = attributeLineV2s.stream().collect(Collectors.groupingBy(ListingAmazonAttributeLineV2::getPropNodePath));
        List<String> allPropNodePaths = new ArrayList<>(attributeLineV2Map.keySet());
        if (CollUtil.isNotEmpty(templateSmcMappings)) {
            allPropNodePaths.addAll(templateSmcMappings.stream().map(AmCategoryTemplateSmcMapping::getPropNodePath).collect(Collectors.toList()));
        }
        allPropNodePaths = allPropNodePaths.stream().distinct().collect(Collectors.toList());

        // 获取该层级对应的所有PROP表数据
        AmCategoryTemplateFieldProp fieldPropQuery = new AmCategoryTemplateFieldProp();
        fieldPropQuery.setProductType(categoryInfo.getProductType());
        fieldPropQuery.setPropNodePathList(allPropNodePaths);
        fieldPropQuery.setVcFlag(vcFlag);
        fieldPropQuery.setSite(categoryInfo.getSite());
        List<AmCategoryTemplateFieldProp> fieldProps = amCategoryTemplateFieldPropService.selectAmCategoryTemplateFieldPropList(fieldPropQuery);
        List<Long> allFieldIds = fieldProps.stream().map(AmCategoryTemplateFieldProp::getRefFieldId).distinct().collect(Collectors.toList());

        AmCategoryTemplateField query = new AmCategoryTemplateField();
        query.setIds(allFieldIds);
        List<AmCategoryTemplateField> templateFields = amCategoryTemplateFieldService.selectAmCategoryTemplateFieldList(query);

        //构建SPEL上下文
        StandardEvaluationContext context = CollUtil.isEmpty(templateSmcMappings) ? new StandardEvaluationContext() : getContext(goodsHead, mappingFields);

        // 遍历templateFields组装JSON map
        JSONObject uploadJson = getJsonObject(goodsHead, context, categoryInfo, templateFields, attributeLineV2Map, mappingFieldMap);
//        handleBeforeReturn(uploadJson, categoryInfo);

        // 上传到接口时，移除value为空的字段，不是刊登接口，不能移除value为空的字段：需要对枚举值做校验，如果移除枚举值就不会校验了
        if (Constants.YesOrNo.NO.equals(onlyGetJson)) {
            cleanJsonObject(uploadJson);
        }
        return uploadJson;
    }

    // 清理JSONArray中的空字符串和空的JSONObject
    private  void cleanJsonArray(JSONArray jsonArray) {
        for (int i = jsonArray.size() - 1; i >= 0; i--) {
            Object value = jsonArray.get(i);

            if (value instanceof JSONObject) {
                cleanJsonObject((JSONObject) value);

                // 如果清理后该JSONObject为空，从JSONArray中移除
                if (((JSONObject) value).isEmpty()) {
                    jsonArray.remove(i);
                }
            } else if (value instanceof JSONArray) {
                cleanJsonArray((JSONArray) value);

                // 如果清理后该JSONArray为空，从JSONArray中移除
                if (((JSONArray) value).isEmpty()) {
                    jsonArray.remove(i);
                }
            } else if (value instanceof String && ((String) value).isEmpty()) {
                // 移除空字符串的元素
                jsonArray.remove(i);
            }
        }
    }

    // 清理JSONObject中的空字符串键值对
    public void cleanJsonObject(JSONObject jsonObject) {
        Iterator<String> keys = jsonObject.keySet().iterator();

        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.get(key);

            if (value instanceof JSONObject) {
                cleanJsonObject((JSONObject) value);

                // 如果清理后该JSONObject为空，移除它
                if (((JSONObject) value).isEmpty()) {
                    keys.remove();
                }
            } else if (value instanceof JSONArray) {
                cleanJsonArray((JSONArray) value);

                // 如果清理后该JSONArray为空，移除它
                if (((JSONArray) value).isEmpty()) {
                    keys.remove();
                }
            } else if (value instanceof String && (StrUtil.isBlank((String) value))) {
                // 移除空字符串的键
                keys.remove();
            }
        }

        // 新增逻辑：检查是否只剩下 'marketplace_id' 或 'language_tag' 字段
        if (shouldRemoveLayer(jsonObject)) {
            jsonObject.keySet().clear();  // 清空该JSONObject的所有键值
        }
    }


    // 检查是否只剩下 'marketplace_id' 或 'language_tag' 字段
    private static boolean shouldRemoveLayer(JSONObject jsonObject) {
        // 获取当前JSONObject中的key数量
        int keyCount = jsonObject.size();
        // 如果仅剩下两个key或者一个key，并且这些key是 'marketplace_id' 或 'language_tag'，则应移除该层
        if (keyCount <= 2 && jsonObject.containsKey("marketplace_id") || jsonObject.containsKey("language_tag")) {
            // 如果只有一个key，它必须是 'marketplace_id' 或 'language_tag'
            if (keyCount == 1 && (jsonObject.containsKey("marketplace_id") || jsonObject.containsKey("language_tag"))) {
                return true;
            }
            // 如果有两个key，则必须这两个都是 'marketplace_id' 和 'language_tag'
            if (keyCount == 2 && jsonObject.containsKey("marketplace_id") && jsonObject.containsKey("language_tag")) {
                return true;
            }
        }
        return false;
    }


    private void addOneAttr(JSONObject uploadJson, PlatformCategory categoryInfo) {
        JSONArray item_name = new JSONArray();
        uploadJson.put("item_name", item_name);

        JSONObject keyword = new JSONObject();
        item_name.add(keyword);
        keyword.put("value", "test for smc");
        keyword.put("marketplace_id", "");
        keyword.put("language_tag", "");
//
//        // 移除uploadJson中的空值，有key但是value是空字符串的,递归
//        removeEmptyValue(uploadJson);


        // 判断是否需豁免
//        if(!uploadJson.containsKey("external_product_id")) {
//            // 需豁免，将supplier_declared_has_product_identifier_exemption指定为true，则无需指定merchant_suggested_asin和externally_assigned_product_identifier
//            JSONArray exemption = new JSONArray();
//            uploadJson.put("supplier_declared_has_product_identifier_exemption", exemption);
//
//            JSONObject exemptionObject = new JSONObject();
//            exemption.add(exemptionObject);
//            exemptionObject.put("value", "true");
//            exemptionObject.put("marketplace_id", "marketplace_id");
//        }
    }

    public static final String ARRAY = "array";
    public static final String OBJECT = "object";

    private JSONObject getJsonObject(GoodsHead goodsHead, StandardEvaluationContext context, PlatformCategory categoryInfo, List<AmCategoryTemplateField> templateFields,
                                     Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map, Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap) {
        JSONObject uploadJson = new JSONObject();
        for (AmCategoryTemplateField templateField : templateFields) {
            if (!ARRAY.equals(templateField.getStructType())) {
                if(goodsHead != null) {
                    throw new BusinessException("商品ID：" + goodsHead.getId() + "，商品编码：" + goodsHead.getPdmGoodsCode() + "，属性：" + templateField.getFieldName() + "，属性类型错误");
                }else {
                    throw new BusinessException("属性：" + templateField.getFieldName() + "，属性类型错误");
                }
            }
            List<AmCategoryTemplateFieldProp> fieldProps = amCategoryTemplateFieldPropService.listByRefField(templateField.getId());
            if (CollUtil.isEmpty(fieldProps)) {
                continue;
            }
            // to map
            Map<Long, List<AmCategoryTemplateFieldProp>> allPropMap = fieldProps.stream().collect(Collectors.groupingBy(AmCategoryTemplateFieldProp::getParentPropId));

            // 第一层级的属性
            List<AmCategoryTemplateFieldProp> firstLevelProps = allPropMap.get(0L);
            // firstLevelProps to map
            Map<Long, List<AmCategoryTemplateFieldProp>> firstLevelPropMap = firstLevelProps.stream().collect(Collectors.groupingBy(AmCategoryTemplateFieldProp::getParentPropId));

            Integer maxUniqueItems = templateField.getMaxUniqueItems();
            // 对应的值是数组，但是限制了只能有一个object，所有相关属性只能加入到一个object
            if (maxUniqueItems != null && maxUniqueItems.equals(1)) {
                JSONArray jsonArray = new JSONArray();
                uploadJson.put(templateField.getFieldCode(), jsonArray);

                JSONObject jsonObject = new JSONObject();
                jsonArray.add(jsonObject);
                getOneObjectArrayValueMap(goodsHead, context, categoryInfo, templateField, firstLevelPropMap, jsonObject, allPropMap, attributeLineV2Map, mappingFieldMap);
            }
            // 对应的值是数组，可以有多个object
            else {
                JSONArray jsonArray = new JSONArray();
                uploadJson.put(templateField.getFieldCode(), jsonArray);

                getArrayValueMap(goodsHead, context, templateField, firstLevelPropMap, jsonArray, attributeLineV2Map, mappingFieldMap, maxUniqueItems, categoryInfo, allPropMap);
            }

        }
        return uploadJson;
    }


    private JSONObject getMockJsonObject(PlatformCategory categoryInfo, List<AmCategoryTemplateField> templateFields) {
        JSONObject uploadJson = new JSONObject();
        for (AmCategoryTemplateField templateField : templateFields) {
            if (!ARRAY.equals(templateField.getStructType())) {
                throw new BusinessException("属性：" + templateField.getFieldName() + "，属性类型错误");
            }

            List<AmCategoryTemplateFieldProp> fieldProps = amCategoryTemplateFieldPropService.listByRefField(templateField.getId());
            if (CollUtil.isEmpty(fieldProps)) {
                continue;
            }
            // to map
            Map<Long, List<AmCategoryTemplateFieldProp>> allPropMap = fieldProps.stream().collect(Collectors.groupingBy(AmCategoryTemplateFieldProp::getParentPropId));

            // 第一层级的属性
            List<AmCategoryTemplateFieldProp> firstLevelProps = allPropMap.get(0L);
            // firstLevelProps to map
            Map<Long, List<AmCategoryTemplateFieldProp>> firstLevelPropMap = firstLevelProps.stream().collect(Collectors.groupingBy(AmCategoryTemplateFieldProp::getParentPropId));

            int maxUniqueItems = templateField.getMaxUniqueItems() == null ? 1 : templateField.getMaxUniqueItems();
            // 对应的值是数组，但是限制了只能有一个object，所有相关属性只能加入到一个object
            if (maxUniqueItems == 1) {
                JSONObject jsonObject = new JSONObject();
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(jsonObject);
                uploadJson.put(templateField.getFieldCode(), jsonArray);
                getMockOneObjectArrayValueMap(firstLevelPropMap, jsonObject, allPropMap);
            }
            // 对应的值是数组，可以有多个object
            else {
                JSONArray jsonArray = new JSONArray();
                uploadJson.put(templateField.getFieldCode(), jsonArray);

                getMockArrayValueMap(templateField, firstLevelPropMap, jsonArray, maxUniqueItems);
            }
        }
        return uploadJson;
    }

    /**
     * 类型是数组，且可以创建多个对象
     *
     * @param templateField
     * @param currentMap
     * @param parentArray
     * @param maxUniqueItems
     */
    private void getMockArrayValueMap(AmCategoryTemplateField templateField, Map<Long, List<AmCategoryTemplateFieldProp>> currentMap, JSONArray parentArray, int maxUniqueItems) {
        for (Map.Entry<Long, List<AmCategoryTemplateFieldProp>> entry : currentMap.entrySet()) {
            List<AmCategoryTemplateFieldProp> fieldProps = entry.getValue();
            JSONObject jsonObject = new JSONObject();
            parentArray.add(jsonObject);
            // 同一层多个属性，创建多个对象
            for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
                // 如果是 marketplace_id 或 language_tag,直接赋值
                if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                    jsonObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                    continue;
                }
                if (Arrays.asList("number", "integer").contains(fieldProp.getStructType())) {
                    jsonObject.put(fieldProp.getPropCode(), 1);
                } else if ("boolean".equals(fieldProp.getStructType())) {
                    jsonObject.put(fieldProp.getPropCode(), "false");
                } else {
                    jsonObject.put(fieldProp.getPropCode(), "mock data");
                }
            }
        }
    }

    /**
     * 类型是数组，且只能创建一个对象
     *
     * @param currentMap
     * @param currentObject
     * @param allPropMap
     */
    private void getMockOneObjectArrayValueMap(Map<Long, List<AmCategoryTemplateFieldProp>> currentMap, JSONObject currentObject,
                                               Map<Long, List<AmCategoryTemplateFieldProp>> allPropMap) {
        for (Map.Entry<Long, List<AmCategoryTemplateFieldProp>> entry : currentMap.entrySet()) {
            List<AmCategoryTemplateFieldProp> fieldProps = entry.getValue();
            // 同一层多个属性，创建多个对象
            for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
                // 如果是 marketplace_id 或 language_tag,直接赋值
                if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                    currentObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                    continue;
                }

                // 当前是对象
                if (!Constants.YesOrNo.YES.equals(fieldProp.getLeafNode())) {
                    if (OBJECT.equals(fieldProp.getStructType())) {
                        JSONObject subObject = new JSONObject();

                        List<AmCategoryTemplateFieldProp> nextLevelProps = allPropMap.values().stream().flatMap(List::stream).filter(a -> fieldProp.getId().equals(a.getParentPropId())).collect(Collectors.toList());
                        if (CollUtil.isEmpty(nextLevelProps)) {
                            continue;
                        }

                        currentObject.put(fieldProp.getPropCode(), subObject);
                        Map<Long, List<AmCategoryTemplateFieldProp>> nextLevelMap = new HashMap<>();
                        nextLevelMap.put(fieldProp.getId(), nextLevelProps);
                        getMockOneObjectArrayValueMap(nextLevelMap, subObject, allPropMap);
                    } else if (ARRAY.equals(fieldProp.getStructType())) {
                        JSONArray subArray = new JSONArray();

                        List<AmCategoryTemplateFieldProp> nextLevelProps = allPropMap.values().stream().flatMap(List::stream).filter(a -> fieldProp.getId().equals(a.getParentPropId())).collect(Collectors.toList());
                        if (CollUtil.isEmpty(nextLevelProps)) {
                            continue;
                        }
                        currentObject.put(fieldProp.getPropCode(), subArray);
                        Map<Long, List<AmCategoryTemplateFieldProp>> nextLevelMap = new HashMap<>();
                        nextLevelMap.put(fieldProp.getId(), nextLevelProps);

                        JSONObject subObject = new JSONObject();
                        subArray.add(subObject);
                        getMockOneObjectArrayValueMap(nextLevelMap, subObject, allPropMap);
                    }
                }
                // 剩下的都是简单类型
                else {
                    if (Arrays.asList("number", "integer").contains(fieldProp.getStructType())) {
                        currentObject.put(fieldProp.getPropCode(), 1);
                    } else if ("boolean".equals(fieldProp.getStructType())) {
                        currentObject.put(fieldProp.getPropCode(), "false");
                    } else {
                        currentObject.put(fieldProp.getPropCode(), "mock data");
                    }
                }
            }
        }
    }

    /**
     * 类型是数组，且只能创建一个对象
     *
     * @param goodsHead
     * @param context
     * @param categoryInfo
     * @param templateField
     * @param currentMap
     * @param currentObject
     * @param allPropMap
     * @param attributeLineV2Map
     * @param mappingFieldMap
     */
    private void getOneObjectArrayValueMap(GoodsHead goodsHead, StandardEvaluationContext context, PlatformCategory categoryInfo, AmCategoryTemplateField templateField,
                                           Map<Long, List<AmCategoryTemplateFieldProp>> currentMap, JSONObject currentObject,
                                           Map<Long, List<AmCategoryTemplateFieldProp>> allPropMap, Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map,
                                           Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap) {
        for (Map.Entry<Long, List<AmCategoryTemplateFieldProp>> entry : currentMap.entrySet()) {
            List<AmCategoryTemplateFieldProp> fieldProps = entry.getValue();
            // 同一层多个属性，创建多个对象
            for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
                // 如果是 marketplace_id 或 language_tag,直接赋值
                if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                    currentObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                    continue;
                }

                // 其他属性，先从line表或者映射表获取值，如果是多个值，本层级创建多个对象加入数组
                List<ListingAmazonAttributeLineV2> attributeLineV2s = attributeLineV2Map.get(fieldProp.getPropNodePath());
                List<AmCategoryTemplateSmcMapping> attributeMappings = mappingFieldMap.get(fieldProp.getPropNodePath());
                int max = Math.max(CollUtil.size(attributeLineV2s), CollUtil.size(attributeMappings));
                // 当前是对象
                if (Constants.YesOrNo.NO.equals(fieldProp.getLeafNode())) {
                    if (OBJECT.equals(fieldProp.getStructType())) {
                        JSONObject subObject = new JSONObject();
                        // 对象这一层级不可能获取到值
                        if (max != 0) {
                            if(goodsHead != null) {
                                throw new BusinessException(String.format("商品ID：%s，商品编码：%s，属性：%s，属性值错误", goodsHead.getId(), goodsHead.getPdmGoodsCode(), fieldProp.getPropName()));
                            }else {
                                throw new BusinessException(String.format("属性：%s，属性值错误", fieldProp.getPropName()));
                            }
                        }
                        List<AmCategoryTemplateFieldProp> nextLevelProps = allPropMap.values().stream().flatMap(List::stream).filter(a -> fieldProp.getId().equals(a.getParentPropId())).collect(Collectors.toList());
                        if (CollUtil.isEmpty(nextLevelProps)) {
                            continue;
                        }

                        currentObject.put(fieldProp.getPropCode(), subObject);
                        Map<Long, List<AmCategoryTemplateFieldProp>> nextLevelMap = new HashMap<>();
                        nextLevelMap.put(fieldProp.getId(), nextLevelProps);
                        getOneObjectArrayValueMap(goodsHead, context, categoryInfo, templateField, nextLevelMap, subObject, allPropMap, attributeLineV2Map, mappingFieldMap);
                    } else if (ARRAY.equals(fieldProp.getStructType())) {
                        JSONArray subArray = new JSONArray();
                        // 数组这一层级不可能获取到值
                        if (max != 0) {
                            if(goodsHead != null) {
                                throw new BusinessException(String.format("商品ID：%s，商品编码：%s，属性：%s，属性值错误", goodsHead.getId(), goodsHead.getPdmGoodsCode(), fieldProp.getPropName()));
                            }else {
                                throw new BusinessException(String.format("属性：%s，属性值错误", fieldProp.getPropName()));
                            }
                        }
                        List<AmCategoryTemplateFieldProp> nextLevelProps = allPropMap.values().stream().flatMap(List::stream).filter(a -> fieldProp.getId().equals(a.getParentPropId())).collect(Collectors.toList());
                        if (CollUtil.isEmpty(nextLevelProps)) {
                            continue;
                        }
                        currentObject.put(fieldProp.getPropCode(), subArray);
                        Map<Long, List<AmCategoryTemplateFieldProp>> nextLevelMap = new HashMap<>();
                        nextLevelMap.put(fieldProp.getId(), nextLevelProps);

                        JSONObject subObject = new JSONObject();
                        subArray.add(subObject);
                        getOneObjectArrayValueMap(goodsHead, context, categoryInfo, templateField, nextLevelMap, subObject, allPropMap, attributeLineV2Map, mappingFieldMap);
                    }
                }
                // 剩下的都是简单类型
                else {
                    Object simpleValue = getSimpleValue(context, attributeLineV2s, attributeMappings, 0, fieldProp, goodsHead);
                    if(simpleValue != null) {
                        currentObject.put(fieldProp.getPropCode(), simpleValue);
                    }
                }
            }
        }
    }

    /**
     * 类型是数组，且可以创建多个对象
     *
     * @param goodsHead
     * @param context
     * @param templateField
     * @param currentMap
     * @param parentArray
     * @param attributeLineV2Map
     * @param mappingFieldMap
     * @param maxUniqueItems
     * @param categoryInfo
     * @param allPropMap
     */
    private void getArrayValueMap(GoodsHead goodsHead, StandardEvaluationContext context, AmCategoryTemplateField templateField,
                                  Map<Long, List<AmCategoryTemplateFieldProp>> currentMap, JSONArray parentArray, Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map,
                                  Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap, Integer maxUniqueItems, PlatformCategory categoryInfo, Map<Long, List<AmCategoryTemplateFieldProp>> allPropMap) {
        for (Map.Entry<Long, List<AmCategoryTemplateFieldProp>> entry : currentMap.entrySet()) {
            List<AmCategoryTemplateFieldProp> fieldProps = entry.getValue();

            boolean containNoLeafNode = fieldProps.stream().anyMatch(a -> Constants.YesOrNo.NO.equals(a.getLeafNode()));
            if (containNoLeafNode) {
                JSONObject jsonObject = new JSONObject();
                parentArray.add(jsonObject);
                getOneObjectArrayValueMap(goodsHead, context, categoryInfo, templateField, currentMap, jsonObject, allPropMap, attributeLineV2Map, mappingFieldMap);
                continue;
            }

            JSONObject jsonObject = new JSONObject();
            // 计算每个属性对应有多少个值的map
            Map<Long, Integer> propValueCountMap = new HashMap<>();
            for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
                List<ListingAmazonAttributeLineV2> attributeLineV2s = attributeLineV2Map.get(fieldProp.getPropNodePath());
                List<AmCategoryTemplateSmcMapping> attributeMappings = mappingFieldMap.get(fieldProp.getPropNodePath());
                int max = Math.max(CollUtil.size(attributeLineV2s), CollUtil.size(attributeMappings));
                propValueCountMap.put(fieldProp.getId(), max);
            }

            // 先找出有多个值的props
            List<AmCategoryTemplateFieldProp> multiValueProps = fieldProps.stream().filter(a -> propValueCountMap.get(a.getId()) > 1).collect(Collectors.toList());
            // 只有一个字段有多个值，根据这个字段创建多个对象
            if (multiValueProps.size() <= 1) {
                parentArray.add(jsonObject);

                if(CollUtil.isEmpty(multiValueProps)) {
                    getOneValueObj(context, attributeLineV2Map, mappingFieldMap, fieldProps, jsonObject, goodsHead);
                }else {
                    getMultiValueObj(goodsHead, context, parentArray, attributeLineV2Map, mappingFieldMap, maxUniqueItems, multiValueProps, fieldProps, jsonObject);
                }
            }
            // 多个字段有多个值
            else {
                // 值的个数是否相等
//                boolean isSame = multiValueProps.stream().map(a -> propValueCountMap.get(a.getId())).distinct().count() == 1;
//                if (!isSame) {
//                    if (goodsHead != null) {
//                        throw new BusinessException(String.format("商品ID：%s，商品编码：%s，属性：%s，属性值个数不一致", goodsHead.getId(), goodsHead.getPdmGoodsCode(), multiValueProps.stream().map(AmCategoryTemplateFieldProp::getPropName).collect(Collectors.joining(","))));
//                    } else {
//                        throw new BusinessException(String.format("属性：%s，属性值个数不一致", multiValueProps.stream().map(AmCategoryTemplateFieldProp::getPropName).collect(Collectors.joining(","))));
//                    }
//                }

                // 相等的话，创建一组对象，最大为propValueCountMap.get(multiValueProps.get(0).getId())
                for (int i = 0; i < propValueCountMap.get(multiValueProps.get(0).getId()); i++) {
                    JSONObject newJsonObject = new JSONObject();
                    jsonObject.putAll(newJsonObject);
                    for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
                        // 如果是 marketplace_id 或 language_tag,直接赋值
                        if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                            newJsonObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                            continue;
                        }
                        // 其他属性，先从line表或者映射表获取值，如果是多个值，本层级创建多个对象加入数组
                        List<ListingAmazonAttributeLineV2> attributeLineV2s = attributeLineV2Map.get(fieldProp.getPropNodePath());
                        List<AmCategoryTemplateSmcMapping> attributeMappings = mappingFieldMap.get(fieldProp.getPropNodePath());
                        Object simpleValue = getSimpleValue(context, attributeLineV2s, attributeMappings, i, fieldProp, goodsHead);
                        if (simpleValue != null) {
                            newJsonObject.put(fieldProp.getPropCode(), simpleValue);
                        }
                    }
                    parentArray.add(newJsonObject);
                }
            }
        }
    }

    private void getOneValueObj(StandardEvaluationContext context, Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map, Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap,
                                List<AmCategoryTemplateFieldProp> fieldProps, JSONObject jsonObject, GoodsHead goodsHead) {
        // 同一层多个属性，创建多个对象
        for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
            // 如果是 marketplace_id 或 language_tag,直接赋值
            if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                jsonObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                continue;
            }

            // 其他属性，先从line表或者映射表获取值，如果是多个值，本层级创建多个对象加入数组
            List<ListingAmazonAttributeLineV2> attributeLineV2s = attributeLineV2Map.get(fieldProp.getPropNodePath());
            List<AmCategoryTemplateSmcMapping> attributeMappings = mappingFieldMap.get(fieldProp.getPropNodePath());
            Object simpleValue = getSimpleValue(context, attributeLineV2s, attributeMappings, 0, fieldProp, goodsHead);
            if(simpleValue != null) {
                jsonObject.put(fieldProp.getPropCode(), simpleValue);
            }
        }
    }
 
    private void getMultiValueObj(GoodsHead goodsHead, StandardEvaluationContext context, JSONArray parentArray, Map<String, List<ListingAmazonAttributeLineV2>> attributeLineV2Map, Map<String, List<AmCategoryTemplateSmcMapping>> mappingFieldMap, Integer maxUniqueItems, List<AmCategoryTemplateFieldProp> multiValueProps, List<AmCategoryTemplateFieldProp> fieldProps, JSONObject jsonObject) {
        AmCategoryTemplateFieldProp multiValueProp = multiValueProps.get(0);
        // 其他属性，先从line表或者映射表获取值，如果是多个值，本层级创建多个对象加入数组
        List<ListingAmazonAttributeLineV2> attributeLineV2s = attributeLineV2Map.get(multiValueProp.getPropNodePath());
        List<AmCategoryTemplateSmcMapping> attributeMappings = mappingFieldMap.get(multiValueProp.getPropNodePath());
        int max = Math.max(CollUtil.size(attributeLineV2s), CollUtil.size(attributeMappings));
        if (maxUniqueItems != null && max > maxUniqueItems) {
            if (goodsHead != null) {
                throw new BusinessException(String.format("商品ID：%s，商品编码：%s，属性：%s，属性值超过最大限制", goodsHead.getId(), goodsHead.getPdmGoodsCode(), multiValueProp.getPropName()));
            } else {
                throw new BusinessException(String.format("属性：%s，属性值超过最大限制", multiValueProp.getPropName()));
            }
        }

        // 同一层多个属性，创建多个对象
        for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
            // 如果是 marketplace_id 或 language_tag,直接赋值
            if (Arrays.asList("marketplace_id", "language_tag").contains(fieldProp.getPropCode())) {
                jsonObject.put(fieldProp.getPropCode(), fieldProp.getPropCode());
                continue;
            }
            List<ListingAmazonAttributeLineV2> target = CollUtil.isNotEmpty(attributeLineV2s) ? attributeLineV2s.stream().filter(a -> fieldProp.getPropNodePath().equals(a.getPropNodePath())).collect(Collectors.toList()) : null;
            Object simpleValue = getSimpleValue(context, target, attributeMappings, 0, fieldProp, goodsHead);
            if(simpleValue != null) {
                jsonObject.put(fieldProp.getPropCode(), simpleValue);
            }
        }

        for (int i = 1; i < max; i++) {
            JSONObject newJsonObject = new JSONObject();
            newJsonObject.putAll(jsonObject);
            Object value = getSimpleValue(context, attributeLineV2s, attributeMappings, i, multiValueProp, goodsHead);
            if (value != null) {
                newJsonObject.put(multiValueProp.getPropCode(), value);
            }
            parentArray.add(newJsonObject);
        }
    }

    private Object getSimpleValue(StandardEvaluationContext context, List<ListingAmazonAttributeLineV2> attributeLineV2s,
                                  List<AmCategoryTemplateSmcMapping> attributeMappings, int index, AmCategoryTemplateFieldProp fieldProp, GoodsHead goodsHead) {
        String result = "";
        if (CollUtil.isNotEmpty(attributeLineV2s)) {
            if(index >= attributeLineV2s.size()) {
                return "";
            }
            result =  attributeLineV2s.get(index).getTableValue();
            if(StrUtil.isBlank(result) || "null".equals(result)) {
                return "";
            }
            return convertValue(result, fieldProp);
        }

        if (CollUtil.isNotEmpty(attributeMappings)) {
            AmCategoryTemplateSmcMapping amCategoryTemplateSmcMapping = attributeMappings.get(index);
            if ("none".equals(amCategoryTemplateSmcMapping.getMappingField())) {
                return "";
            }
            
            if (goodsHead != null && goodsHead.getNonResource() != null && goodsHead.getNonResource() && amCategoryTemplateSmcMapping.getPropNodePath().contains("image")) {
                return "";
            }

            String value = SpelUtil.parse(amCategoryTemplateSmcMapping.getMappingField(), context);

            // 如果是包装规格，需要单独处理下规格
            if ((amCategoryTemplateSmcMapping.getPropNodePath().contains("unit"))) {
                value = StringUtils.toFullUnit(value);
            }

            if (ObjUtil.equals(value, amCategoryTemplateSmcMapping.getMappingField())) {
                value = "";
            }
            if("null".equals(value)) {
                value = "";
            }
            return convertValue(value, fieldProp);
        }
        return "";
    }

    private Object convertValue(String value, AmCategoryTemplateFieldProp fieldProp) {
        if(StringUtils.isNotBlank(value) && StrUtil.isNotBlank(fieldProp.getStructType())) {
            try {
                value = StrUtil.trim(value);
                if(Arrays.asList("number", "integer").contains(fieldProp.getStructType())) {
                    return new BigDecimal(value);
                } else if ("boolean".equals(fieldProp.getStructType())) {
                    return Boolean.parseBoolean(value);
                }
            }catch (Exception e) {
                log.error("转换属性值异常", e);
            }
        }
        return value;
    }


    public StandardEvaluationContext getContext(GoodsHead goodsHead, List<String> mappingFields) {
        if (ObjUtil.isEmpty(goodsHead) || ObjUtil.isEmpty(goodsHead.getId())) {
            return new StandardEvaluationContext();
        }

        // 默认相关表数据都作为SPEL的上下文
        if (ObjUtil.isEmpty(mappingFields)) {
            mappingFields = Lists.newArrayList("goodsResource", "goodsDescription", "goodsSpecification", "goodsHead");
        }

        Integer headId = goodsHead.getId();
        // 获取商品资源
        List<GoodsResource> goodsResourceList = mappingFields.toString().contains("goodsResource") ? goodsResourceService.selectListingGoodsResourceByHeadId(headId) : null;
        // 获取商品描述
        GoodsDescription goodsDescription = mappingFields.toString().contains("goodsDescription") ? goodsDescriptionService.selectDescriptionListByGoodsId(headId) : null;
        // 获取商品规格
        GoodsSpecification goodsSpecification = mappingFields.toString().contains("goodsSpecification") ? goodsSpecificationService.selectSpecificationListByGoodsId(headId) : null;
        //获取类目信息
        PlatformCategory platformCategory = mappingFields.toString().contains("platformCategory") ? platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId())) : null;

        // 构建SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("goodsHead", goodsHead);
        context.setVariable("goodsDescription", goodsDescription == null ? new GoodsDescription() : goodsDescription);
        context.setVariable("goodsSpecification", goodsSpecification == null ? new GoodsSpecification() : goodsSpecification);
        context.setVariable("platformCategory", platformCategory == null ? new PlatformCategory() : platformCategory);
        if (CollUtil.isNotEmpty(goodsResourceList)) {
            goodsHead.setNonResource(false);
            try {
                imageHandleBiz.replaceAMResourcesUrl(goodsResourceList, goodsHead.getPublishType(), goodsHead.getShopCode(), null);
            } catch (Exception e) {
                log.error(String.format("商品ID：%s，商品编码：%s，替换图片资源URL异常", goodsHead.getId(), goodsHead.getPdmGoodsCode()), e);
            }

            goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(headId);
            GoodsResourceDTO goodsResourceDTO = getGoodsResourceDTO(goodsResourceList);
            context.setVariable("goodsResource", goodsResourceDTO);
        } else {
            goodsHead.setNonResource(true);
        }
        return context;
    }


    private GoodsResourceDTO getGoodsResourceDTO(List<GoodsResource> goodsResourceList) {
        GoodsResourceDTO goodsResourceDTO = new GoodsResourceDTO();
        // 主图，
        goodsResourceDTO.setResourceUrl1(goodsResourceList.stream().filter(e -> Objects.equals(e.getIsMain(), 1)).findFirst().map(GoodsResource::getResourceUrl).orElse(null));

        // goodsResourceList先按sortNumber排序，注意非空
        goodsResourceList = goodsResourceList.stream().filter(e -> !Objects.equals(e.getIsMain(), 1)).collect(Collectors.toList());
        if (CollUtil.isEmpty(goodsResourceList)) {
            return goodsResourceDTO;
        }
        // 对goodsResourceList按sortNumber排序，注意有值的就排序，没有值的放在末尾
        goodsResourceList.sort(Comparator.comparingInt(e -> {
            if (e.getSortNumber() == null) {
                return Integer.MAX_VALUE;
            }
            return e.getSortNumber();
        }));


        // 直接按排序的结果，赋值给goodsResourceDTO的资源url, 不能依据sortNumber赋值
        goodsResourceDTO.setResourceUrl2(goodsResourceList.size() > 0 ? goodsResourceList.get(0).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl3(goodsResourceList.size() > 1 ? goodsResourceList.get(1).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl4(goodsResourceList.size() > 2 ? goodsResourceList.get(2).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl5(goodsResourceList.size() > 3 ? goodsResourceList.get(3).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl6(goodsResourceList.size() > 4 ? goodsResourceList.get(4).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl7(goodsResourceList.size() > 5 ? goodsResourceList.get(5).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl8(goodsResourceList.size() > 6 ? goodsResourceList.get(6).getResourceUrl() : null);
        goodsResourceDTO.setResourceUrl9(goodsResourceList.size() > 7 ? goodsResourceList.get(7).getResourceUrl() : null);
        return goodsResourceDTO;
    }

    private String getValue(StandardEvaluationContext context, String nodePath) {
        if (ObjUtil.isEmpty(nodePath)) {
            return "";
        }
        if (nodePath.contains("language_tag")) {
            return "language_tag";
        }
        if (nodePath.contains("marketplace_id")) {
            return "marketplace_id";
        }
        String value = SpelUtil.parse(nodePath, context);
        if (ObjUtil.equals(value, nodePath)) {
            return "";
        }
        return value;
    }

    private Map<String, Object> getValue(Map<String, Map<String, Object>> fieldValueMap, StandardEvaluationContext context, String codePath, List<AmCategoryTemplateFieldProp> fieldProps,
                                         List<AmCategoryTemplateFieldProp> fieldPropsChild) {
        Map<String, Object> attributeMap = new HashMap<>();
        for (AmCategoryTemplateFieldProp fieldProp : fieldPropsChild) {
            Map<String, Object> valueMap = fieldValueMap.get(fieldProp.getPropNodePath());

            String value = null;
            String expression = valueMap != null && valueMap.containsKey("expression") ? (String) valueMap.get("expression") : "";
            // 不需要SPEL表达式，直接从valueMap中取值
            if ("N".equals(expression)) {
                value = valueMap.get("value").toString();
            } else {
                value = getValue(context, ObjUtil.isEmpty(expression) ? fieldProp.getPropNodePath() : expression);
            }

            if (StrUtil.isBlank(value) && !codePath.contains(fieldProp.getPropNodePath())) {
                continue;
            }
            if (StrUtil.isNotEmpty(value) || ObjUtil.equals(codePath, fieldProp.getPropNodePath())) {
                attributeMap.put(fieldProp.getPropCode(), value);
                continue;
            }

            Long id = fieldProp.getId();
            List<AmCategoryTemplateFieldProp> attributeChild = fieldProps.stream().filter(prop -> ObjUtil.equals(prop.getParentPropId(), id)).collect(Collectors.toList());
            Map<String, Object> valueChild = getValue(fieldValueMap, context, codePath, fieldProps, attributeChild);
            if (ObjUtil.equals(fieldProp.getStructType(), "array")) {
                attributeMap.put(fieldProp.getPropCode(), Lists.newArrayList(valueChild));
            } else if (ObjUtil.equals(fieldProp.getStructType(), "object")) {
                attributeMap.put(fieldProp.getPropCode(), valueChild);
            }
        }
        return attributeMap;
    }

    private void checkRequired(AmazonPushDTO amazonPushDTO) {
        if (CollUtil.isEmpty(amazonPushDTO.getGoodsHeadVOS())) {
            throw new BusinessException("商品信息不能为空");
        }
        if (CollUtil.isEmpty(amazonPushDTO.getGoodsTaskTypes())) {
            throw new BusinessException("商品任务类型不能为空");
        }
        if (amazonPushDTO.getCategoryInfo() == null) {
            throw new BusinessException("平台品类信息不能为空");
        }
        if (StringUtils.isBlank(amazonPushDTO.getOperationType())) {
            throw new BusinessException("操作类型不能为空");
        }
    }

    public void getCategoryRequired(String productType, String shopCode) {
        PlatformCategory query = new PlatformCategory();
        query.setPlatformCode(AM.name());
        if (StringUtils.isNotBlank(productType)) {
            query.setProductType(productType);
        }
        Shop shop = shopService.selectShopByShopCode(shopCode);
        query.setSite(shop.getSiteCode());
        List<PlatformCategory> platformCategories = platformCategoryService.selectPlatformCategoryList(query);
        if (CollUtil.isEmpty(platformCategories)) {
            return;
        }
        String vcFlag = shopCode.contains("VC") ? Constants.YesOrNo.YES : Constants.YesOrNo.NO;
        // 按productType分组
        Map<String, List<PlatformCategory>> productTypeMap = platformCategories.stream().filter(e -> StringUtils.isNotBlank(e.getProductType())).collect(Collectors.groupingBy(PlatformCategory::getProductType));

        // 20个一批，信号量
        Semaphore semaphore = new Semaphore(20);
        for (Map.Entry<String, List<PlatformCategory>> stringListEntry : productTypeMap.entrySet()) {
            // 线程池执行
            ExecutorService executor = threadPoolForMonitorManager.getThreadPoolExecutor("amazon-asin-callback");
            try {
                semaphore.acquire();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            executor.execute(() -> {
                try {
                    PlatformCategory categoryInfo = stringListEntry.getValue().get(0);
                    if (StringUtils.isBlank(categoryInfo.getProductType())) {
                        throw new BusinessException("平台品类信息中的productType不能为空");
                    }
                    AmCategoryTemplateFieldProp fieldPropQuery = new AmCategoryTemplateFieldProp();
                    fieldPropQuery.setProductType(categoryInfo.getProductType());
                    fieldPropQuery.setVcFlag(vcFlag);
                    fieldPropQuery.setSite(categoryInfo.getSite());
                    List<AmCategoryTemplateFieldProp> fieldProps = amCategoryTemplateFieldPropService.selectAmCategoryTemplateFieldPropList(fieldPropQuery);
                    if (CollUtil.isEmpty(fieldProps)) {
                        throw new BusinessException("平台品类信息中的productType对应的属性信息不能为空");
                    }

                    JSONObject uploadJson = new JSONObject();
                    addOneAttr(uploadJson, categoryInfo);
                    // 通过uploadSkuV2方法，提前获取必要的属性
                    AmazonListingJSONFeedVO feed = AmazonListingJSONFeedVO.builder()
                            .sku("test-required-sku")
                            .productType(categoryInfo.getProductType())
                            .sellerCode(shopCode)
                            .attributes(uploadJson)
                            .build();
                    AjaxResult ajaxResult = amazonApiHttpRequestBiz.uploadSkuV2(vcFlag.equals(Constants.YesOrNo.YES) ? PublishType.VCDF.getType() : PublishType.FBM.getType(), feed);
                    if (ajaxResult.isSuccess()) {
                        throw new BusinessException("获取必要属性失败，productType:" + stringListEntry.getKey());
                    }
                    String msg = ajaxResult.get("msg").toString();

                    doSaveRequired(msg, fieldProps, true, vcFlag);

                    // mock部分字段的值，暂定10个，传10个字段的值，继续调用uploadSkuV2方法，获取必要属性，一次最多30个属性必填
                    List<AmCategoryTemplateFieldProp> mockFieldProps = fieldProps.stream()
                            .filter(a -> Constants.YesOrNo.YES.equals(a.getUploadRequired()) && Constants.YesOrNo.YES.equals(a.getLeafNode()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(mockFieldProps)) {
                        List<String> enumFields = mockFieldProps.stream().filter(a -> Constants.YesOrNo.YES.equals(a.getEnumFlag())).map(AmCategoryTemplateFieldProp::getFieldCode).distinct().collect(Collectors.toList());
                        List<Long> allFieldIds = mockFieldProps.stream().map(AmCategoryTemplateFieldProp::getRefFieldId).distinct().collect(Collectors.toList());
                        AmCategoryTemplateField query2 = new AmCategoryTemplateField();
                        query2.setIds(allFieldIds);
                        List<AmCategoryTemplateField> templateFields = amCategoryTemplateFieldService.selectAmCategoryTemplateFieldList(query2);
                        // 过滤掉不能存在枚举的字段
                        templateFields = templateFields.stream().filter(a -> !enumFields.contains(a.getFieldCode())).limit(10).collect(Collectors.toList());
                        uploadJson.putAll(getMockJsonObject(categoryInfo, templateFields));

                        feed = AmazonListingJSONFeedVO.builder()
                                .sku("test-required-sku")
                                .productType(categoryInfo.getProductType())
                                .sellerCode(shopCode)
                                .attributes(uploadJson)
                                .build();
                        ajaxResult = amazonApiHttpRequestBiz.uploadSkuV2(PublishType.VCDF.getType(), feed);
                        if (ajaxResult.isSuccess()) {
                            throw new BusinessException("获取必要属性失败");
                        }

                        msg = ajaxResult.get("msg").toString();
                        doSaveRequired(msg, fieldProps, false, vcFlag);
                    }
                } catch (Exception e) {
                    log.error("同步亚马逊类目失败,productType:{}", stringListEntry.getKey(), e);
                } finally {
                    semaphore.release();
                }
            });
        }
    }
    

    private void doSaveRequired(String msg, List<AmCategoryTemplateFieldProp> fieldProps, boolean isFirst, String vcFlag) {
        // {"issues":[{"attributeNames":["included_components"],"code":"90220","message":"'included_components' is required but not supplied.","severity":"ERROR"},{"attributeNames":["model_number"],"code":"90220","message":"'model_number' is required but not supplied.","severity":"ERROR"},{"attributeNames":["part_number"],"code":"90220","message":"'part_number' is required but not supplied.","severity":"ERROR"},{"attributeNames":["cost_price"],"code":"90220","message":"'cost_price' is required but not supplied.","severity":"ERROR"},{"attributeNames":["warranty_description"],"code":"90220","message":"'warranty_description' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_name"],"code":"90220","message":"'item_name' is required but not supplied.","severity":"ERROR"},{"attributeNames":["required_product_compliance_certificate"],"code":"90220","message":"'required_product_compliance_certificate' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_type_name"],"code":"90220","message":"'item_type_name' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_name"],"code":"8560","message":"SKU test-required-sku, Missing Attributes item_name. SKU test-required-sku doesn't match any ASINs. Make sure that all standard product ids (such as UPC, ISBN, EAN, or JAN codes) are correct. To create a new ASIN, include the following attributes: item_name. Feed ID: 0. For more troubleshooting help, see http://vendorcentral.amazon.com/help/hub?ref=/gp/errorcode/200692370","severity":"ERROR"},{"attributeNames":["country_of_origin"],"code":"90220","message":"'country_of_origin' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_package_weight"],"code":"90220","message":"'item_package_weight' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_name","part_number"],"code":"8560","message":"SKU test-required-sku, Missing Attributes item_name. SKU test-required-sku doesn't match any ASINs. Make sure that all standard product ids (such as UPC, ISBN, EAN, or JAN codes) are correct. To create a new ASIN, include the following attributes: item_name. Feed ID: 0. For more troubleshooting help, see http://vendorcentral.amazon.com/help/hub?ref=/gp/errorcode/200692370","severity":"ERROR"},{"attributeNames":["model_name"],"code":"90220","message":"'model_name' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_package_dimensions"],"code":"90220","message":"'item_package_dimensions' is required but not supplied.","severity":"ERROR"},{"attributeNames":["rtip_items_per_inner_pack"],"code":"90220","message":"'rtip_items_per_inner_pack' is required but not supplied.","severity":"ERROR"},{"attributeNames":["list_price"],"code":"90220","message":"'list_price' is required but not supplied.","severity":"ERROR"},{"attributeNames":["brand"],"code":"90220","message":"'brand' is required but not supplied.","severity":"ERROR"},{"attributeNames":["supplier_declared_dg_hz_regulation"],"code":"90220","message":"'supplier_declared_dg_hz_regulation' is required but not supplied.","severity":"ERROR"},{"attributeNames":["automotive_fit_type"],"code":"90220","message":"'automotive_fit_type' is required but not supplied.","severity":"ERROR"},{"attributeNames":["package_level"],"code":"90220","message":"'package_level' is required but not supplied.","severity":"ERROR"},{"attributeNames":["is_expiration_dated_product"],"code":"90220","message":"'is_expiration_dated_product' is required but not supplied.","severity":"ERROR"},{"attributeNames":["item_name","manufacturer"],"code":"8560","message":"SKU test-required-sku, Missing Attributes item_name. SKU test-required-sku doesn't match any ASINs. Make sure that all standard product ids (such as UPC, ISBN, EAN, or JAN codes) are correct. To create a new ASIN, include the following attributes: item_name. Feed ID: 0. For more troubleshooting help, see http://vendorcentral.amazon.com/help/hub?ref=/gp/errorcode/200692370","severity":"ERROR"},{"attributeNames":["manufacturer"],"code":"90220","message":"'manufacturer' is required but not supplied.","severity":"ERROR"},{"attributeNames":["product_category"],"code":"4000002","message":"A value for 'product_category' is required.","severity":"ERROR"},{"attributeNames":["fit_type"],"code":"90220","message":"'fit_type' is required but not supplied.","severity":"ERROR"},{"attributeNames":["bullet_point"],"code":"90220","message":"'bullet_point' is required but not supplied.","severity":"ERROR"},{"attributeNames":["product_subcategory"],"code":"4000002","message":"A value for 'product_subcategory' is required.","severity":"ERROR"},{"attributeNames":["number_of_items"],"code":"90220","message":"'number_of_items' is required but not supplied.","severity":"ERROR"},{"attributeNames":["rtip_product_description"],"code":"90220","message":"'rtip_product_description' is required but not supplied.","severity":"ERROR"}],"sku":"test-required-sku","status":"INVALID","submissionId":"acbd01966d0d40969cccdb9800922887"}
        if (!JSONUtil.isJson(msg)) {
            log.error("获取必要属性失败，msg：{}", msg);
            return;
        }
        List<String> requiredFields;
        if(Constants.YesOrNo.NO.equals(vcFlag)) {
            requiredFields = scErrorFieldHandle(msg);
        }else {
            requiredFields = vcErrorFieldHandle(msg);
        }

        for (AmCategoryTemplateFieldProp fieldProp : fieldProps) {
            if (containsRequiredField(fieldProp.getPropNodePath(), requiredFields)) {
                fieldProp.setUploadRequired(Constants.YesOrNo.YES);
                amCategoryTemplateFieldPropService.updateAmCategoryTemplateFieldProp(fieldProp);
            } else {
                if (isFirst) {
                    fieldProp.setUploadRequired(Constants.YesOrNo.NO);
                    amCategoryTemplateFieldPropService.updateAmCategoryTemplateFieldProp(fieldProp);
                }
            }
        }
    }

    /**
     * SC刊登错误字段处理
     * @param msg
     * @return
     */
    private List<String> scErrorFieldHandle(String msg) {
        JSONArray errors = JSONArray.parseArray(msg);
        // ["[recommended_browse_nodes]'recommended_browse_nodes' is required but not supplied.","[included_components]'included_components' is required but not supplied.","[batteries_required]'batteries_required' is required but not supplied.","[part_number]'part_number' is required but not supplied.","[product_description]'product_description' is required but not supplied.","[number_of_boxes]'number_of_boxes' is required but not supplied.","[fit_type]'fit_type' is required but not supplied.","[externally_assigned_product_identifier]'externally_assigned_product_identifier' is required but not supplied.","[bullet_point]'bullet_point' is required but not supplied.","[automotive_fit_type]'automotive_fit_type' is required but not supplied.","[supplier_declared_dg_hz_regulation]'supplier_declared_dg_hz_regulation' is required but not supplied.","[condition_type]'condition_type' is required but not supplied.","[item_package_dimensions]'item_package_dimensions' is required but not supplied.","[merchant_suggested_asin]'merchant_suggested_asin' is required but not supplied.","[manufacturer]'manufacturer' is required but not supplied.","[item_package_weight]'item_package_weight' is required but not supplied.","[is_assembly_required]'is_assembly_required' is required but not supplied.","[is_fragile]'is_fragile' is required but not supplied.","[list_price]'list_price' is required but not supplied.","[product_grade]'product_grade' is required but not supplied.","[brand]'brand' is required but not supplied.","[is_expiration_dated_product]'is_expiration_dated_product' is required but not supplied.","[country_of_origin]'country_of_origin' is required but not supplied.","[model_name]'model_name' is required but not supplied.","[warranty_description]'warranty_description' is required but not supplied."]
        List<String> requiredFields = Lists.newArrayList();
        for (int i = 0; i < errors.size(); i++) {
            String error = errors.getString(i);
            if (error.contains("is required but not supplied")) {
                requiredFields.add(error.substring(1, error.indexOf("]")));
            }
        }
        return requiredFields;
    }

    /**
     * VC刊登错误字段处理
     * @param msg
     * @return
     */
    private List<String>  vcErrorFieldHandle(String msg) {
        JSONObject jsonObject = JSONObject.parseObject(msg);
        if (!jsonObject.containsKey("issues")) {
            throw new BusinessException("获取必要属性失败");
        }
        JSONArray issues = jsonObject.getJSONArray("issues");
        List<String> requiredFields = Lists.newArrayList();
        for (int i = 0; i < issues.size(); i++) {
            JSONObject issue = issues.getJSONObject(i);
            int code = issue.getIntValue("code");
            if (!Arrays.asList(4000002, 90220).contains(code)) {
                continue;
            }
            requiredFields.addAll(issue.getJSONArray("attributeNames").toJavaList(String.class));
        }

        if (CollUtil.isEmpty(requiredFields)) {
            log.error("获取必要属性失败，requiredFields为空,msg：{}", msg);
            throw new BusinessException("获取必要属性失败");
        }
        return requiredFields;
    }

    private boolean containsRequiredField(String fieldProp, List<String> requiredFieldsCode) {
        for (String requiredField : requiredFieldsCode) {
            if (fieldProp.contains(requiredField)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 定时任务获取ASIN
     */
    public void jobCallbackAsin() {
        // 查询1天内的数据
        Date date = DateUtils.addDays(new Date(), -1);
        String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date);

        GoodsHead query = new GoodsHead();
        query.setPlatform(AM.name());
        query.setQueryTime(dateStr);
        query.setPublishStatusList(Lists.newArrayList(PublishStatus.PUBLISHING.getType()));
        
        // 优先按创建时间排序，确保早刊登的商品优先处理
        PageHelper.orderBy("create_time asc, update_time asc");
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(query);
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }
        
        // 按刊登时长对商品进行分组
        Map<PriorityLevel, List<GoodsHead>> prioritizedGoods = groupGoodsByPublishTime(goodsHeads);
        
        // 按优先级顺序处理商品
        processGoodsByPriority(prioritizedGoods);
    }
    
    /**
     * 商品刊登优先级枚举
     */
    private enum PriorityLevel {
        HIGH,    // 刊登时间长（>30分钟）的商品，最高优先级
        MEDIUM,  // 刊登时间中等（5-30分钟）的商品，中等优先级
        LOW      // 新刊登（<5分钟）的商品，低优先级
    }
    
    /**
     * 根据刊登时间将商品分组
     * @param goodsHeads 待处理的商品列表
     * @return 按优先级分组的商品
     */
    private Map<PriorityLevel, List<GoodsHead>> groupGoodsByPublishTime(List<GoodsHead> goodsHeads) {
        Map<PriorityLevel, List<GoodsHead>> result = new HashMap<>();
        result.put(PriorityLevel.HIGH, new ArrayList<>());
        result.put(PriorityLevel.MEDIUM, new ArrayList<>());
        result.put(PriorityLevel.LOW, new ArrayList<>());
        
        Date now = DateUtils.getNowDate();
        
        for (GoodsHead goodsHead : goodsHeads) {
            String processTimeKey = String.format("amazon:v2:processTime:%s", goodsHead.getId());
            Date firstProcessTime = redisService.getCacheObject(processTimeKey);
            
            // 如果Redis中没有记录，则跳过,必须保证调用过刊登接口
            if (firstProcessTime == null) {
                continue;
            }
            
            long diffMinutes = (now.getTime() - firstProcessTime.getTime()) / (60 * 1000);
            
            // 根据时间差分配优先级
            if (diffMinutes > 30) {
                result.get(PriorityLevel.HIGH).add(goodsHead);
            } else if (diffMinutes > 5) {
                result.get(PriorityLevel.MEDIUM).add(goodsHead);
            } else {
                result.get(PriorityLevel.LOW).add(goodsHead);
            }
        }
        
        return result;
    }
    
    /**
     * 按优先级顺序处理商品
     * @param prioritizedGoods 按优先级分组的商品
     */
    private void processGoodsByPriority(Map<PriorityLevel, List<GoodsHead>> prioritizedGoods) {
        // 创建一个线程池，用于并行处理不同优先级的商品
        // 高优先级的商品单独处理，中低优先级的商品可以并行处理
        ExecutorService executor = threadPoolForMonitorManager.getThreadPoolExecutor("amazon-asin-callback");
        
        try {
            // 统计各优先级商品数量
            int highCount = prioritizedGoods.get(PriorityLevel.HIGH).size();
            int mediumCount = prioritizedGoods.get(PriorityLevel.MEDIUM).size();
            int lowCount = prioritizedGoods.get(PriorityLevel.LOW).size();
            
            log.info("ASIN回写任务开始，商品总数: {}，优先级分布 - 高: {}，中: {}，低: {}", 
                    highCount + mediumCount + lowCount, highCount, mediumCount, lowCount);
            
            // 1. 处理高优先级商品（串行处理，确保立即处理）
            // 高优先级组减少超时时间到45分钟，更快标记失败状态
            if (!prioritizedGoods.get(PriorityLevel.HIGH).isEmpty()) {
                log.info("开始处理高优先级商品组（>30分钟），数量: {}, 超时设置: {}分钟", highCount, 45);
                executor.submit(() -> processGoodsHeadsList(prioritizedGoods.get(PriorityLevel.HIGH), 45, 500));
            }
            
            // 2. 处理中优先级商品（可以并行处理）
            if (!prioritizedGoods.get(PriorityLevel.MEDIUM).isEmpty()) {
                log.info("开始处理中优先级商品组（5-30分钟），数量: {}, 超时设置: {}分钟", mediumCount, 50);
                executor.submit(() -> processGoodsHeadsList(prioritizedGoods.get(PriorityLevel.MEDIUM), 50, 1000));
            }
            
            // 3. 处理低优先级商品（也可并行处理，但优先级低）
            if (!prioritizedGoods.get(PriorityLevel.LOW).isEmpty()) {
                log.info("开始处理低优先级商品组（<5分钟），数量: {}, 超时设置: {}分钟", lowCount, 60);
                executor.submit(() -> processGoodsHeadsList(prioritizedGoods.get(PriorityLevel.LOW), 60, 2000));
            }
        } catch (Exception e) {
            log.error("处理Amazon商品回调ASIN时发生异常", e);
        }
    }
    
    /**
     * 处理商品列表，获取ASIN并回写
     * @param goodsHeads 待处理的商品列表
     * @param timeoutMinutes 超时时间（分钟）
     * @param sleepMillis 处理间隔（毫秒）
     */
    private void processGoodsHeadsList(List<GoodsHead> goodsHeads, int timeoutMinutes, long sleepMillis) {
        for (GoodsHead goodsHead : goodsHeads) {
            try {
                processGoodsHeadAsin(goodsHead, timeoutMinutes);
                
                // 添加间隔，避免请求过于频繁
                if (sleepMillis > 0) {
                    Thread.sleep(sleepMillis);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("处理商品ASIN时被中断, 商品ID: {}", goodsHead.getId(), e);
            } catch (Exception e) {
                log.error("处理商品ASIN时出错, 商品ID: {}", goodsHead.getId(), e);
            }
        }
    }
    
    /**
     * 处理单个商品的ASIN回调
     * @param goodsHead 待处理的商品
     * @param timeoutMinutes 超时时间（分钟）
     */
    private void processGoodsHeadAsin(GoodsHead goodsHead, int timeoutMinutes) {
        long startTime = System.currentTimeMillis();
        
        // 查询最新的数据
        GoodsHead dbGoodsHead = goodsHeadService.selectListingGoodsHeadById(goodsHead.getId());
        // 检查状态是否已经改变
        if (StringUtils.isNotBlank(dbGoodsHead.getPlatformGoodsId()) || !Objects.equals(dbGoodsHead.getPublishStatus(), goodsHead.getPublishStatus())) {
            log.error("商品状态已变更，跳过处理，headId：{}", goodsHead.getId());
            return;
        }

        final String redisKey = String.format("amazon:v2:upload:%s:%s", goodsHead.getId(), goodsHead.getShopCode());
        final String processTimeKey = String.format("amazon:v2:processTime:%s", goodsHead.getId());

        try {
            RLock lock = redissonClient.getLock(redisKey);
            boolean locked = lock.isLocked();
            if (locked) {
                log.info("商品ID：{}，商品编码：{}，正在处理中", goodsHead.getId(), goodsHead.getPdmGoodsCode());
                return;
            }

            // 尝试获取锁，避免并发处理
            boolean getLock = false;
            try {
                getLock = lock.tryLock(500, 10000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("获取锁被中断，商品ID：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode(), e);
                return;
            }
            
            if (!getLock) {
                log.error("获取处理锁失败，跳过处理，商品ID：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode());
                return;
            }
            
            try {
                // 记录商品首次处理时间
                Date firstProcessTime = redisService.getCacheObject(processTimeKey);
                if (firstProcessTime == null) {
                    return;
                } else {
                    long processMinutes = (DateUtils.getNowDate().getTime() - firstProcessTime.getTime()) / (60 * 1000);
                    log.info("重新处理商品，已处理时间：{}分钟，商品ID：{}，商品编码：{}", processMinutes, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                }
    
                //通过skuCode接口获取asin、fnsku
                log.info("开始调用亚马逊API获取商品详情，商品ID：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode());
                AjaxResult detailResultSku = amazonApiHttpRequestBiz.getDetailBySkuV2(goodsHead.getShopCode(), goodsHead.getPublishType(), goodsHead.getPlatformGoodsCode());
                
                if (!detailResultSku.isSuccess()) {
                    log.error("获取商品详情失败，商品ID：{}，商品编码：{}，错误信息：{}", 
                            goodsHead.getId(), goodsHead.getPdmGoodsCode(), detailResultSku.get(AjaxResult.MSG_TAG));
                    
                    // 动态超时判断：根据优先级设置不同的超时时间
                    long processedMinutes = (DateUtils.getNowDate().getTime() - firstProcessTime.getTime()) / (60 * 1000);
                    if (processedMinutes > timeoutMinutes) {
                        log.error("商品处理超时（{}分钟），标记为失败，商品ID：{}，商品编码：{}", timeoutMinutes, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                        if (ObjUtil.isNotEmpty(detailResultSku) && ObjUtil.isNotEmpty(detailResultSku.get(AjaxResult.DATA_TAG))) {
                            JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResultSku.get(AjaxResult.DATA_TAG)));
                            if (ObjUtil.isNotEmpty(itemInfo) && ObjUtil.isNotEmpty(itemInfo.get("issues"))) {
                                JSONArray issues = itemInfo.getJSONArray("issues");
                                log.error("亚马逊返回错误信息：{}，商品ID：{}，商品编码：{}", issues, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                                insertListingLog("上架", itemInfo.toJSONString(), goodsHead);
                            }else {
                                insertListingLog("上架", "刊登失败：亚马逊未返回ASIN，请检查属性后重新刊登", goodsHead);
                            }
                        } else {
                            insertListingLog("上架", "刊登失败：亚马逊未返回ASIN，请检查属性后重新刊登", goodsHead);
                        }
                        redisService.deleteObject(processTimeKey);
                    } else {
                        log.info("商品尚未处理超时，稍后重试，当前处理时间：{}分钟，超时阈值：{}分钟，商品ID：{}，商品编码：{}", 
                                processedMinutes, timeoutMinutes, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                    }
                    return;
                }
                
                log.info("成功获取商品详情，商品ID：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode());
                JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResultSku.get(AjaxResult.DATA_TAG)));
                String asin = callbackAsin(itemInfo, goodsHead, "上架");
                
                if (StringUtils.isNotBlank(asin)) {
                    log.info("成功获取ASIN：{}，商品ID：{}，商品编码：{}", asin, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                    callbackPublish(goodsHead);
                    redisService.deleteObject(processTimeKey);
                } else if (PublishStatus.PUBLISHING.getType().equals(goodsHead.getPublishStatus())) {
                    long processedMinutes = (DateUtils.getNowDate().getTime() - firstProcessTime.getTime()) / (60 * 1000);
                    
                    if (processedMinutes > timeoutMinutes) {
                        log.error("商品处理超时（{}分钟）且未获取到ASIN，标记为失败，商品ID：{}，商品编码：{}", 
                                timeoutMinutes, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                        
                        // 动态超时判断：使用传入的超时时间
                        JSONArray issues = itemInfo.getJSONArray("issues");
                        if (CollUtil.isNotEmpty(issues)) {
                            log.error("亚马逊返回错误信息：{}，商品ID：{}，商品编码：{}", issues, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                            insertListingLog("上架", itemInfo.toJSONString(), goodsHead);
                        } else {
                            insertListingLog("上架", "刊登失败：亚马逊未返回ASIN，请检查属性后重新刊登", goodsHead);
                        }
                        redisService.deleteObject(processTimeKey);
                    } else {
                        log.info("未获取到ASIN但尚未超时，继续等待，当前处理时间：{}分钟，超时阈值：{}分钟，商品ID：{}，商品编码：{}", 
                                processedMinutes, timeoutMinutes, goodsHead.getId(), goodsHead.getPdmGoodsCode());
                    }
                }
            } finally {
                // 释放锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("获取ASIN失败，商品ID：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode(), e);
        }
        
        long endTime = System.currentTimeMillis();
        log.info("处理商品完成，耗时：{}毫秒，商品ID：{}，商品编码：{}", (endTime - startTime), goodsHead.getId(), goodsHead.getPdmGoodsCode());
    }

    /**
     * am库存更新 兼容插入日志以及备份统计
     *
     * @param onlineList
     * @param shop
     * @param inventoryMap
     * @param type
     */
    public void updateAmazonStock(List<GoodsHead> onlineList, ConfigStoreInfo shop, Map<String, Integer> inventoryMap, String type) {

        Map<Boolean, List<GoodsHead>> hcMap = onlineList.stream().collect(Collectors.partitioningBy(g -> g.getShopCode() != null && g.getShopCode().contains("VC")));
        List<GoodsHead> vcList = hcMap.get(true);
        List<GoodsHead> fbmList = hcMap.get(false);

        // 库存服务 灰度sku vc渠道 smc不处理
        String graySku = sysConfigService.selectConfigByKey("inventory.update.gray.sku");
        if (ObjectUtil.isNotEmpty(graySku)) {
            List<String> graySkuList = Arrays.asList(graySku.split(","));
            if (ObjectUtil.isNotEmpty(graySkuList)) {
                vcList = vcList.stream().filter(g -> !graySkuList.contains(g.getPdmGoodsCode())).collect(Collectors.toList());
            }
        }

        //补偿 vc记录表标记失败的进行更新
        compensationVcInventoryUpdate(shop, inventoryMap);

        if (CollUtil.isNotEmpty(vcList)) {
            List<AmazonWarehouseMapping> warehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode(shop.getShopCode());
            // 处理VC链接的库存 - 使用SKU总库存与总库存下限比较
            handleVcInventory(shop, inventoryMap, warehouseMappings, vcList);

            Map<String, String> whCodeMap = warehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, AmazonWarehouseMapping::getWhCode));

            List<Integer> goodsId = vcList.stream().map(GoodsHead::getId).collect(Collectors.toList());
            List<VcListingInventory> listingInventoryList = vcListingInventoryService.selectVcListingInventoryByGoodsIdList(goodsId);
            Map<String, List<VcListingInventory>> listingInventoryMap = listingInventoryList.stream().collect(Collectors.groupingBy(VcListingInventory::getWarehouseCode));

            listingInventoryMap.forEach((amWhCde, inventoryList) -> {
                String whCode = whCodeMap.get(amWhCde);
                List<VcListingInventory> vcInventoryUpdate = buildVcInventoryUpdate(shop, inventoryMap, whCode, inventoryList);
                baseAmazonProductUpdateV2Task.updateInventoryAmazonVc(shop, amWhCde, vcInventoryUpdate, null);
            });
        }

        if (CollUtil.isNotEmpty(fbmList)) {
            for (GoodsHead head : fbmList) {
                BigDecimal nowStock = BigDecimal.valueOf(baseAmazonProductTask.getQuantity(shop, inventoryMap, head));
                head.setStockOnSalesQty(nowStock);
                if ("库存服务-库存校准".equals(type) && BigDecimal.ZERO.equals(nowStock)) {
                    log.info("库存服务-库存校准,smc库存与仓库库存均为0不做更新,商品ID:{},商品编码:{}", head.getId(), head.getPdmGoodsCode());
                    continue;
                }
                baseAmazonProductUpdateV2Task.updateInventoryAmazonV2(head, type);
            }
        }
        addInventoryBackUp(onlineList);
    }

    /**
     * 处理VC链接的库存 - 使用SKU总库存与总库存下限比较
     *
     * @param shop
     * @param inventoryMap
     * @param warehouseMappings
     * @param vcList
     */
    private void handleVcInventory(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, List<AmazonWarehouseMapping> warehouseMappings, List<GoodsHead> vcList) {
        // 获取所有仓库代码
        List<String> whCodes = warehouseMappings.stream()
                .map(AmazonWarehouseMapping::getWhCode)
                .collect(Collectors.toList());

        // 处理每个SKU
        for (GoodsHead head : vcList) {
            String sku = head.getPdmGoodsCode();
            String site = shop.getSite();

            // 获取SKU的总库存下限
            Integer quantityFloor = thirdpartyInventoryBiz.getSkuQuantityFloor(sku, "TOTAL", shop.getShopCode());
            if (quantityFloor == null) {
                // 默认值
                quantityFloor = 2;
            }

            // 计算所有仓库的库存总和
            int totalStock = 0;
            for (String whCode : whCodes) {
                Integer stock = inventoryMap.get(site + whCode + sku);
                if (stock == null) {
                    //兼容map中没有对应库存信息
                    List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBiz.selectFbmStockByShareAndPartGoodsCode(Collections.singletonList(sku), true);
                    //合并仓库库存
                    thirdpartyInventoryBiz.groupByWarehouseCode(thirdpartyFbmDTOList);
                    for (ThirdpartyFbmDTO fbmDTO : thirdpartyFbmDTOList) {
                        inventoryMap.put(fbmDTO.getWhCountry() + fbmDTO.getWarehouseCode() + fbmDTO.getSku(), fbmDTO.getSellableQty());
                    }
                    stock = inventoryMap.get(site + whCode + sku);
                }
                if (stock != null) {
                    totalStock += stock;
                }
            }

            log.info("VC商品:[{}] 站点:[{}] 所有仓库总库存:[{}], 库存下限:[{}]", sku, site, totalStock, quantityFloor);

            // 如果总库存小于等于库存下限，所有仓库库存设为0  否则保持各仓库的实际库存不变
            if (totalStock <= quantityFloor) {
                for (String whCode : whCodes) {
                    inventoryMap.put(site + whCode + sku, 0);
                }
                log.info("VC商品:[{}] 总库存小于等于库存下限，所有仓库库存设为0", sku);
            }
        }
    }

    /**
     * 补偿vc库存更新记录表失败的记录，再次进行更新
     */
    private void compensationVcInventoryUpdate(ConfigStoreInfo shop, Map<String, Integer> inventoryMap) {
        if (ObjUtil.isEmpty(shop) || ObjUtil.isEmpty(shop.getShopCode()) || !shop.getShopCode().contains("VC")) {
            return;
        }
        List<AmazonWarehouseMapping> warehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode(shop.getShopCode());
        Map<String, String> whCodeMap = warehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, AmazonWarehouseMapping::getWhCode));
        if (ObjUtil.isEmpty(whCodeMap)) {
            return;
        }
        List<VcInventoryRecord> inventoryRecordList = vcInventoryRecordService.selectFailList();
        for (VcInventoryRecord record : inventoryRecordList) {
            //不处理错误信息包含ITEM_SUPPRESSED的，并把状态置为已处理 防止重复扫描
            if (record.getErrorMsg().contains(VcDfConfirmTransactionStatus.VcDfInventoryUpdateErrorEnum.ITEM_SUPPRESSED.getCode())) {
                record.setStatus(SMCCommonEnum.PROCESSED.getValue());
                record.setUpdateTime(new Date());
                vcInventoryRecordService.updateVcInventoryRecord(record);
                continue;
            }
            String amWhCode = record.getAmWhCode();
            String whCode = whCodeMap.get(amWhCode);
            String submitData = record.getSubmitData();
            if (ObjUtil.isEmpty(whCode) || ObjUtil.isEmpty(whCode) || ObjUtil.isEmpty(submitData)) {
                log.error("补偿vc-df库存更新失败,请确认vc-df更新记录数据正常,ID:{}", record.getId());
                continue;
            }
            List<VcListingInventory> vcListingInventoryList = JSON.parseArray(submitData, VcListingInventory.class);
            List<String> skuList = vcListingInventoryList.stream().map(VcListingInventory::getSku).collect(Collectors.toList());
            if (CollUtil.isEmpty(skuList)) {
                log.error("补偿vc-df库存更新失败,获取sku失败,请确认vc-df更新记录数据正常,ID:{}", record.getId());
                continue;
            }
            //获取新的库存完成更新
            List<VcListingInventory> newUpdateList = new ArrayList<>();
            for (VcListingInventory vcListingInventory : vcListingInventoryList) {
                String sku = vcListingInventory.getSku();
                Integer inventory = inventoryMap.get(shop.getSite() + whCode + sku);
                if (ObjUtil.isEmpty(inventory)) {
                    List<ThirdpartyFbmDTO> nowList = thirdpartyInventoryBiz.selectFbmStockByShareAndPartGoodsCode(skuList, true);
                    thirdpartyInventoryBiz.groupByWarehouseCode(nowList);
                    ThirdpartyFbmDTO fbmDTO = nowList.stream().filter(n -> Objects.equals(n.getSku(), sku)
                            && Objects.equals(whCode, n.getWarehouseCode())
                            && Objects.equals(shop.getSite(), n.getWhCountry())).findFirst().orElse(null);
                    if (ObjUtil.isEmpty(fbmDTO)) {
                        log.error("补偿库存更新失败,获取库存失败,listing平台商品编码:{},商品编码:{}", vcListingInventory.getSellerSku(), vcListingInventory.getSku());
                        continue;
                    }
                    inventory = fbmDTO.getSellableQty();
                    inventoryMap.put(shop.getSite() + whCode + sku, inventory);
                }
                vcListingInventory.setAvailableInventory(inventory);
                newUpdateList.add(vcListingInventory);
            }
            if (CollUtil.isNotEmpty(newUpdateList)) {
                baseAmazonProductUpdateV2Task.updateInventoryAmazonVc(shop, amWhCode, newUpdateList, null);
            }
            //标记已处理
            record.setStatus(SMCCommonEnum.PROCESSED.getValue());
            vcInventoryRecordService.updateVcInventoryRecord(record);
        }
    }

    /**
     * 构建vc库存更新的参数对象
     *
     * @param shop
     * @param inventoryMap
     * @param whCode
     * @param inventoryList
     * @return
     */
    public List<VcListingInventory> buildVcInventoryUpdate(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, String whCode, List<VcListingInventory> inventoryList) {
        List<VcListingInventory> updatedList = new ArrayList<>();
        String sameFlag = sysConfigService.selectConfigByKey("inventory.vc.same.flag");
        if (ObjectUtil.isEmpty(whCode)) {
            for (VcListingInventory inventory : inventoryList) {
                VcListingInventory updateRecord = new VcListingInventory();
                updateRecord.setSellerSku(inventory.getSellerSku());
                updateRecord.setSku(inventory.getSku());
                updateRecord.setAsin(inventory.getAsin());
                updateRecord.setAvailableInventory(0);
                updatedList.add(updateRecord);
            }
        } else {
            for (VcListingInventory inventory : inventoryList) {
                String sku = inventory.getSku();
                String site = shop.getSite();
                Integer whQuantity = inventoryMap.get(site + whCode + sku);
                if (ObjectUtils.isEmpty(whQuantity)) {
                    List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = thirdpartyInventoryBiz.selectFbmStockByShareAndPartGoodsCode(Collections.singletonList(sku), true);
                    thirdpartyInventoryBiz.groupByWarehouseCode(thirdpartyFbmDTOList);
                    ThirdpartyFbmDTO fbmDTO = thirdpartyFbmDTOList.stream().filter(t -> Objects.equals(t.getWarehouseCode(), whCode) && Objects.equals(t.getSku(), sku) && Objects.equals(t.getWhCountry(), site)).findFirst().orElse(null);
                    if (ObjectUtils.isEmpty(thirdpartyFbmDTOList) || ObjectUtils.isEmpty(fbmDTO)) {
                        //vc某个仓库查询为空 直接把库存更新为0
                        whQuantity = 0;
                    } else {
                        whQuantity = fbmDTO.getSellableQty();
                    }
                    inventoryMap.put(site + whCode + sku, whQuantity);
                }

                // 如果库存值与当前值相同，不需要更新
                if (Objects.equals("yes", sameFlag) && Objects.equals(whQuantity, inventory.getAvailableInventory())) {
                    continue;
                }

                VcListingInventory updateRecord = new VcListingInventory();
                updateRecord.setSellerSku(inventory.getSellerSku());
                updateRecord.setSku(inventory.getSku());
                updateRecord.setAsin(inventory.getAsin());
                updateRecord.setAvailableInventory(whQuantity);
                updatedList.add(updateRecord);
            }
        }
        return updatedList;
    }

    /**
     * 构建vc库存更新的参数对象
     * 无vc库存报告数据根据库存映射关系进行构建
     *
     * @param shop
     * @param goodsHeadList
     * @param inventoryMap
     * @param whCode
     * @return
     */
    public List<VcListingInventory> buildVcInventoryUpdateByNoReport(ConfigStoreInfo shop, List<GoodsHead> goodsHeadList, Map<String, Integer> inventoryMap, String whCode) {
        if (ObjUtil.isEmpty(goodsHeadList)) {
            return new ArrayList<>();
        }
        List<VcListingInventory> updateList = new ArrayList<>();
        for (GoodsHead goodsHead : goodsHeadList) {
            String sku = goodsHead.getPdmGoodsCode();
            String sellerSku = goodsHead.getPlatformGoodsCode();
            String asin = goodsHead.getPlatformGoodsId();
            if (ObjUtil.isEmpty(sku) || ObjUtil.isEmpty(sellerSku) || ObjUtil.isEmpty(asin)) {
                continue;
            }
            Integer whQuantity = baseAmazonProductTask.getWhQuantity(shop, inventoryMap, sku, whCode);
            VcListingInventory updateRecord = new VcListingInventory();
            updateRecord.setSellerSku(sellerSku);
            updateRecord.setSku(sku);
            updateRecord.setAsin(asin);
            updateRecord.setAvailableInventory(whQuantity);
            updateList.add(updateRecord);
        }

        return updateList;
    }

    /**
     * 构建vc库存更新的参数对象
     * 无vc库存报告数据根据库存映射关系进行构建
     *
     * @param goodsHeadList
     * @return
     */
    public List<VcListingInventory> buildVcZeroInventoryUpdateByNoReport(List<GoodsHead> goodsHeadList) {
        if (ObjUtil.isEmpty(goodsHeadList)) {
            return new ArrayList<>();
        }
        List<VcListingInventory> updateList = new ArrayList<>();
        for (GoodsHead goodsHead : goodsHeadList) {
            String sku = goodsHead.getPdmGoodsCode();
            String sellerSku = goodsHead.getPlatformGoodsCode();
            String asin = goodsHead.getPlatformGoodsId();
            if (ObjUtil.isEmpty(sku) || ObjUtil.isEmpty(sellerSku) || ObjUtil.isEmpty(asin)) {
                continue;
            }
            VcListingInventory updateRecord = new VcListingInventory();
            updateRecord.setSellerSku(sellerSku);
            updateRecord.setSku(sku);
            updateRecord.setGoodsId(goodsHead.getId());
            updateRecord.setAsin(asin);
            updateRecord.setAvailableInventory(0);
            updateList.add(updateRecord);
        }

        return updateList;
    }


    /**
     * 备份库存数据 用于监控
     *
     * @param headList
     */
    private void addInventoryBackUp(List<GoodsHead> headList) {
        List<List<GoodsHead>> backUpList = Lists.partition(headList, 500);
        for (List<GoodsHead> goodsHeadList : backUpList) {
            GoodsHeadBackup backup = new GoodsHeadBackup();
            backup.setShopCode(goodsHeadList.get(0).getShopCode());
            backup.setGoodsIds(goodsHeadList.stream().map(GoodsHead::getId).map(String::valueOf).collect(Collectors.toList()));
            backup.setRemark(OperTypeEnum.INVENTORY_UPDATE.name());
            //删除备份表前3天的数据 ——>物理删除
            goodsHeadBackupService.deleteGoodsHeadBackupByTime(backup);
            List<GoodsHeadBackup> headBackupList = goodsHeadBackupService.selectGoodsHeadBackupByGoodsIds(goodsHeadList.stream().map(GoodsHead::getId).map(String::valueOf).collect(Collectors.toList()), OperTypeEnum.INVENTORY_UPDATE.name());

            //清空库存更新上一次的更新记录 ——>逻辑删除
            if (ObjectUtils.isNotEmpty(headBackupList)) {
                goodsHeadBackupService.deleteGoodsHeadBackup(backup);
            }

            List<GoodsHeadBackup> goodsHeadBackupList = new ArrayList<>();
            for (GoodsHead goodsHead : goodsHeadList) {
                GoodsHeadBackup goodsHeadBackup = new GoodsHeadBackup();
                goodsHeadBackup.setShopCode(goodsHead.getShopCode());
                goodsHeadBackup.setGoodsId(String.valueOf(goodsHead.getId()));
                goodsHeadBackup.setContext(JSON.toJSONString(goodsHead));
                goodsHeadBackup.setCreateBy(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                goodsHeadBackup.setUpdateBy(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                goodsHeadBackup.setCreateTime(DateUtils.getNowDate());
                goodsHeadBackup.setUpdateTime(DateUtils.getNowDate());
                goodsHeadBackup.setRemark(OperTypeEnum.INVENTORY_UPDATE.name());
                goodsHeadBackupList.add(goodsHeadBackup);
            }
            //添加库存备份记录
            goodsHeadBackupService.insertGoodsHeadBackupBatch(goodsHeadBackupList);

        }
    }



    public void refreshVCCategory(String userId) {
        if (StringUtils.isBlank(userId)) {
            return;
        }

        int pageNum = 1;
        int pageSize = 100;
        PageHelper.startPage(pageNum, pageSize);
        List<GoodsHead> goodsHeads = goodsHeadService.listFailListing(userId);
        if (CollectionUtils.isEmpty(goodsHeads)) {
            return;
        }
        refreshVCCategory(goodsHeads);
        int pages = ((Page<GoodsHead>)goodsHeads).getPages();
        for (int i = pageNum + 1; i <= pages; i++) {
            PageHelper.startPage(i, pageSize);
            goodsHeads = goodsHeadService.listFailListing(userId);
            refreshVCCategory(goodsHeads);
        }
    }

    private void refreshVCCategory(List<GoodsHead> goodsHeads) {
        // 按品类分组
        Map<Integer, List<GoodsHead>> categoryMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getCategoryId));
        categoryMap.forEach((k, v) -> {
            doRefreshJsonCategory(k, v, "VC");
        });
    }

    /**
     * 刷新listing属性/以及用户私有配置的属性
     * 支持VC/SC json版本
     *
     * @param categoryId
     * @param goodsHeadList
     * @param type
     */
    public void doRefreshJsonCategory(Integer categoryId, List<GoodsHead> goodsHeadList, String type) {
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(categoryId.longValue());
        if (platformCategory == null || StrUtil.isBlank(platformCategory.getProductType())) {
            return;
        }
        Map<String, List<AmCategoryTemplatePrivateValue>> privateValueCacheMap = new HashMap<>();


        for (GoodsHead goodsHead : goodsHeadList) {
            String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y" : "N";

            List<String> attributeV2Names = AmazonAttributeEnum.getEnumValuesByFlag(vcFlag);

            String privateValueKey = goodsHead.getSiteCode() + "_" + goodsHead.getShopCode() + "_" + goodsHead.getCategoryId() + "_" + platformCategory.getProductType() + "_" + goodsHead.getCreateBy();
            List<AmCategoryTemplatePrivateValue> privateValues = null;
            if (privateValueCacheMap.containsKey(privateValueKey)) {
                privateValues = privateValueCacheMap.get(privateValueKey);
                if (CollUtil.isEmpty(privateValues)) {
                    continue;
                }
            }else {
                // 找到当前用户配置的值
                AmCategoryTemplatePrivateValue privateQuery = new AmCategoryTemplatePrivateValue();
                privateQuery.setSite(goodsHead.getSiteCode());
                privateQuery.setCategoryId(platformCategory.getId());
                privateQuery.setShopCode(goodsHead.getShopCode());
                privateQuery.setProductType(platformCategory.getProductType());
                privateQuery.setVcFlag(Objects.equals(type, "VC") ? "Y":"N");
                privateQuery.setCreateBy(goodsHead.getCreateBy());
                privateValues = amCategoryTemplatePrivateValueService.selectAmCategoryTemplatePrivateValueList(privateQuery);
                privateValueCacheMap.put(privateValueKey, privateValues);
            }
            if (CollUtil.isEmpty(privateValues)) {
                continue;
            }
            // 当前listing的属性
            List<ListingAmazonAttributeLineV2> attributeLineV2s = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHead.getId());
            attributeLineV2s.removeIf(e -> e.getTableType() != null && e.getTableType().equals(5));
            
            // 获取没有填写属性值的属性，将品类配置的值更新到属性表
            privateValues.forEach(privateValue -> {
                ListingAmazonAttributeLineV2 attributeLineV2 = attributeLineV2s.stream().filter(a -> privateValue.getPropNodePath().equals(a.getPropNodePath())).findFirst().orElse(null);
                if (attributeLineV2 == null) {
                    attributeLineV2 = getListingAmazonAttributeLineV2(goodsHead, privateValue, platformCategory, attributeV2Names);
                    listingAmazonAttributeLineV2Service.insertListingAmazonAttributeLineV2(attributeLineV2);
                }else {
                    if (StrUtil.isBlank(attributeLineV2.getTableValue())) {
                        attributeLineV2.setTableValue(privateValue.getValue());
                        attributeLineV2.setTableType(attributeV2Names.contains(privateValue.getPropNodePath()) ? 4 : 0);
                        listingAmazonAttributeLineV2Service.updateListingAmazonAttributeLineV2(attributeLineV2);
                    }
                }
            });
        }
    }

    private static ListingAmazonAttributeLineV2 getListingAmazonAttributeLineV2(GoodsHead goodsHead, AmCategoryTemplatePrivateValue privateValue, PlatformCategory platformCategory, List<String> attributeV2Names) {
        ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
        attributeLineV2.setHeadId(goodsHead.getId().longValue());
        attributeLineV2.setPropNodePath(privateValue.getPropNodePath());
        attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
        attributeLineV2.setProductType(platformCategory.getProductType());
        attributeLineV2.setCategoryId(platformCategory.getId().intValue());
        attributeLineV2.setTableValue(privateValue.getValue());
        attributeLineV2.setTableType(attributeV2Names.contains(privateValue.getPropNodePath()) ? 4 : 0);
        attributeLineV2.setCreateBy(goodsHead.getCreateBy());
        return attributeLineV2;
    }

    public void complementAttribute(Integer headId, String type) {
        if (headId == null) {
            return;
        }
        GoodsHead copiedHead = goodsHeadService.selectListingGoodsHeadById(headId);
        doRefreshJsonCategory(copiedHead.getCategoryId(), Lists.newArrayList(copiedHead), type);
    }

    public boolean comparativeAttribute(GoodsHead goodsHead, List<ListingAmazonAttributeLineV2> newLines) {
        List<ListingAmazonAttributeLineV2> oldLines = listingAmazonAttributeLineV2Service.listByGoodsId(goodsHead.getId());
        if (CollUtil.isEmpty(oldLines)) {
            return true;
        }
        
        // 使用propNodePath和propertyGroupId的组合作为键，以区分相同路径但不同组的属性
        Map<String, List<String>> oldMap = oldLines.stream().collect(
            Collectors.groupingBy(
                attr -> generateAttributeKey(attr.getPropNodePath(), attr.getPropertyGroupId()),
                Collectors.mapping(ListingAmazonAttributeLineV2::getTableValue, Collectors.toList())
            )
        );
        
        Map<String, List<String>> newMap = newLines.stream().collect(
            Collectors.groupingBy(
                attr -> generateAttributeKey(attr.getPropNodePath(), attr.getPropertyGroupId()),
                Collectors.mapping(ListingAmazonAttributeLineV2::getTableValue, Collectors.toList())
            )
        );
        
        for (Map.Entry<String, List<String>> entry : oldMap.entrySet()) {
            String attributeKey = entry.getKey();
            List<String> oldValues = entry.getValue();
            List<String> newValues = newMap.get(attributeKey);
            if (CollUtil.isEmpty(newValues)) {
                return true;
            }
            if (CollUtil.isEmpty(oldValues)) {
                return true;
            }
            if (!new HashSet<>(oldValues).containsAll(newValues)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成属性键，用于区分相同路径但不同组的属性
     * 
     * @param propNodePath 属性路径
     * @param propertyGroupId 属性组ID
     * @return 组合键
     */
    private String generateAttributeKey(String propNodePath, String propertyGroupId) {
        if (StrUtil.isBlank(propertyGroupId)) {
            return propNodePath;
        }
        return propNodePath + "#" + propertyGroupId;
    }

    /**
     * 方案：
     * 1、新建一张待分析-临时表，专门存储ASIN，同时加入唯一索引
     * 2、扫描超过270天的SC链接，将ASIN插入到临时表中
     * 3、扫描超过270天的VC链接，将ASIN插入到临时表中
     * 从临时表一次获取100个ASIN，进行以下判断：
     * 1、移除有销量、评论、星级大于4的ASIN
     * 2、基于上一步的结果，获取对应的链接
     * 同ASIN可能有FBA，FBM、VCPO、VCDF
     * 3、先判断主链接是否可以删除，规则：FBA不能有库存，VCPO不能有库存
     * 4、只有一条链接，直接删除
     * 5、有多条链接，考虑跟卖关系
     * 6、存在VCPO的链接库存，不能删除主链接，SC链接不做处理，原因：可能VCPO跟卖的是主链接
     * 7、存在FBA的链接库存，不能删除主链接，可能FBA链接跟卖的是主链接
     * 8、即没有VCPO库存，也没有FBA库存，可以删除主链接和VCPO链接和FBA链接
     * 9、VCDF链接根据主链接是否可删除，如果主链接可删除，VCDF链接也可删除
     *
     */
    public void generateAmazonDeleteListing() {
        String dataBatch = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        goodsHeadService.deleteTempAsinListAll();

        Date before270 = DateUtils.addDays(new Date(), -270);

        // 上架超过270天的SC链接
        insertScAsinList();

        // 上架超过270天的VC链接
        insertVcAsinList();

        // 获取ASIN
        List<String> asinList = goodsHeadService.listTempAsinList();
        while (CollUtil.isNotEmpty(asinList)) {
            handleAsin(asinList, before270, dataBatch, 1, null);
            asinList = goodsHeadService.listTempAsinList();
        }

        doGenerateBrandShrinkageDeleteListing(dataBatch);
    }

    public void doGenerateBrandShrinkageDeleteListing(String dataBatch) {
        Date before90 = DateUtils.addDays(new Date(), -90);
        // 品牌收缩生成待删除待办
        generateBrandShrinkageDeleteListing(before90);

        generateBrandShirk(dataBatch, before90);
    }

    public void generateBrandShirk(String dataBatch, Date before90) {
        List<String> asinList = goodsHeadService.listTempAsinList();
        if (CollUtil.isEmpty(asinList)) {
            return;
        }
        List<AmBrandLimitConfig> amBrandLimitConfigs = amBrandLimitConfigService.selectAmBrandLimitConfigList(new AmBrandLimitConfig());
        Map<String, AmBrandLimitConfig> brandMap = amBrandLimitConfigs.stream().collect(Collectors.toMap(AmBrandLimitConfig::getBrand, e -> e, (k1, k2) -> k1));

        while (CollUtil.isNotEmpty(asinList)) {
            handleAsin(asinList, before90, dataBatch, 2, brandMap);
            asinList = goodsHeadService.listTempAsinList();
        }
    }


    public void generateBrandShrinkageDeleteListing(Date before90) {
        // 获取所有品牌收缩配置
        AmBrandLimitConfig query = new AmBrandLimitConfig();
        query.setMainFlag(0);
        List<AmBrandLimitConfig> amBrandLimitConfigs = amBrandLimitConfigService.selectAmBrandLimitConfigList(query);
        if (CollUtil.isEmpty(amBrandLimitConfigs)) {
            return;
        }
        // 获取品牌下的所有链接
        for (AmBrandLimitConfig amBrandLimitConfig : amBrandLimitConfigs) {
            long lastId = 0;
            // 获取品牌下的所有AM链接
            List<GoodsHead> goodsHeads = goodsHeadService.selectAMListingByBrand(amBrandLimitConfig.getBrand(), lastId, before90);
            if (CollUtil.isEmpty(goodsHeads)) {
                continue;
            }
            // 获取链接的ASIN
            List<String> asinList = goodsHeads.stream().map(GoodsHead::getPlatformGoodsId).collect(Collectors.toList());
            if (CollUtil.isEmpty(asinList)) {
                continue;
            }
            // 新增到temp asin
            goodsHeadService.insertScAsinList(asinList);

            while (CollUtil.isNotEmpty(asinList)) {
                lastId = goodsHeads.get(goodsHeads.size() - 1).getId();
                goodsHeads = goodsHeadService.selectAMListingByBrand(amBrandLimitConfig.getBrand(), lastId, before90);
                if (CollUtil.isEmpty(goodsHeads)) {
                    break;
                }
                asinList = goodsHeads.stream().map(GoodsHead::getPlatformGoodsId).collect(Collectors.toList());
                if (CollUtil.isEmpty(asinList)) {
                    break;
                }
                goodsHeadService.insertScAsinList(asinList);
            }
        }
    }

    private void handleAsin(List<String> asinList, Date beforeDate, String dataBatch, Integer type, Map<String, AmBrandLimitConfig> brandMap) {
        if (CollUtil.isEmpty(asinList)) {
            return;
        }
        log.info("过滤前ASIN数量：{}", asinList.size());
        asinList = asinList.stream().filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        // 移除有销量、评论、星级大于4的ASIN
        List<String> filteredAsin = removeAsinBySalesReview(asinList, type);
        log.info("过滤后ASIN数量：{}", filteredAsin.size());

        if (CollUtil.isNotEmpty(filteredAsin)) {
            // 对链接继续分析
            handleAsinListing(filteredAsin, beforeDate, dataBatch, type, brandMap);
        }
        // 删除临时表中的ASIN
        goodsHeadService.deleteTempAsinList(asinList);
    }

    /**
     * 处理ASIN对应的链接
     *
     * @param asinList
     * @param beforeDate
     * @param dataBatch
     * @param type
     */
    private void handleAsinListing(List<String> asinList, Date beforeDate, String dataBatch, Integer type, Map<String, AmBrandLimitConfig> brandMap) {
        List<GoodsHead> goodsHeads = goodsHeadService.selectGoodsHeadByAsin(asinList, AM.name());
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }
        Map<String, List<GoodsHead>> asinMap = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getPlatformGoodsId));

        // VC如果上架时间为null，使用BI的上架时间补全
        List<OdsCrlCrlVcCatalogData> vcCatalogDataList = odsCrlCrlVcCatalogDataService.selectByVcAsinList(asinList);
        Map<String, List<OdsCrlCrlVcCatalogData>> vcAsinMap = vcCatalogDataList.stream().collect(Collectors.groupingBy(OdsCrlCrlVcCatalogData::getAsin));

        // 获取VCPO链接库存数据
        Map<String, BigDecimal> vcInventoryMap = getVcPoInventoryMap(asinList);

        for (String asin : asinList) {
            try {
                if (!asinMap.containsKey(asin)) {
                    log.error("ASIN：{}，SMC不存在对应的链接", asin);
                    continue;
                }

                List<GoodsHead> asinGoodsHeads = asinMap.get(asin);
                // 商品编码不能为空
                asinGoodsHeads = asinGoodsHeads.stream().filter(g -> StrUtil.isNotBlank(g.getPdmGoodsCode())).collect(Collectors.toList());
                if (CollUtil.isEmpty(asinGoodsHeads)) {
                    log.error("ASIN：{}，SMC对应的商品编码为空", asin);
                    continue;
                }
                if (type.equals(2)) {
                    // 品牌收缩，过滤配置
                    asinGoodsHeads = filterBrandShrinkage(asinGoodsHeads, brandMap);
                }
                
                if (CollUtil.isEmpty(asinGoodsHeads)) {
                    log.error("ASIN：{}，SMC对应的链接为空", asin);
                    continue;
                }
                
                if (type.equals(1)) {
                    boolean isSupport = isSupportDelete(asinGoodsHeads);
                    if (!isSupport) {
                        log.error("ASIN：{}，不支持删除", asin);
                        continue;
                    }
                }
               
                // 是否存在VC链接并且VC上架时间为null
                List<GoodsHead> vcListOnlineTimeEmpty = asinGoodsHeads.stream().filter(g -> g.getShopCode().contains("VC") && g.getOnlineTime() == null).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(vcListOnlineTimeEmpty)) {
                    fillVCOnlineTime(asin, vcAsinMap, vcListOnlineTimeEmpty);
                }

                // 仍然没有上架时间的链接，不处理
                List<GoodsHead> emptyOnlineTime = asinGoodsHeads.stream().filter(g -> g.getOnlineTime() == null).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(emptyOnlineTime)) {
                    log.error("ASIN：{}，存在上架时间为空的链接", asin);
                    continue;
                }

                // 是否存在beforeDate之前的链接
                List<GoodsHead> containsBeforeListing = asinGoodsHeads.stream().filter(g -> g.getOnlineTime().after(beforeDate)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(containsBeforeListing)) {
                    log.error("ASIN：{}，存在beforeDate之前的链接，不处理", asin);
                    continue;
                }

                // 对异常数据拦截，由另一个任务专门清理异常数据
                List<GoodsHead> vcGoodsHeadList = asinGoodsHeads.stream().filter(g -> Arrays.asList(PublishType.VCDF.getType(), PublishType.VCPO.getType()).contains(g.getPublishType())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(vcGoodsHeadList) && vcGoodsHeadList.size() > 2) {
                    log.error("ASIN：{}，存在2个以上的VC链接，不处理", asin);
                    continue;
                }

                generateDeleteListingMonitor(asin, asinGoodsHeads, vcInventoryMap, dataBatch, type);
            }catch (Exception e) {
                log.error("ASIN：{}，处理异常", asin, e);
            }
        }
    }

    private List<GoodsHead> filterBrandShrinkage(List<GoodsHead> goodsHeads, Map<String, AmBrandLimitConfig> brandMap) {
        List<GoodsHead> resultList = Lists.newArrayList();
        List<String> pdmGoodsCodes = goodsHeads.stream().map(GoodsHead::getPdmGoodsCode).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(pdmGoodsCodes)) {
            return resultList;
        }

        List<GoodsDetailDTO> goodsDetail = pdmHttpRequestBiz.getGoodsDetail(pdmGoodsCodes);
        Map<String, GoodsDetailDTO> goodsDetailMap = goodsDetail.stream().collect(Collectors.toMap(GoodsDetailDTO::getGoodsCode, goods -> goods, (k1, k2) -> k1));
        // 存在多个品牌，之间返回
        Set<String> brandSet = goodsHeads.stream().map(GoodsHead::getBrandCode).collect(Collectors.toSet());
        if (brandSet.size() > 1) {
            return resultList;
        }

        AmBrandLimitConfig amBrandLimitConfig = brandMap.get(goodsHeads.get(0).getBrandCode());
        if (amBrandLimitConfig == null) {
            return resultList;
        }
        if ("all".equals(amBrandLimitConfig.getDeleteExcludeCategory())) {
            return resultList;
        }

        List<String> excludeCategoryList = Arrays.asList(amBrandLimitConfig.getDeleteExcludeCategory().split(","));
        for (GoodsHead goodsHead : goodsHeads) {    
            GoodsDetailDTO goodsDetailDTO = goodsDetailMap.get(goodsHead.getPdmGoodsCode());
            if (goodsDetailDTO == null) {
                continue;
            }
            if(!"2".equals(goodsDetailDTO.getClassificationCode())) {
                continue;
            }
            if (excludeCategoryList.contains(goodsDetailDTO.getProductCategoryName())) {
                continue;
            }
            resultList.add(goodsHead);
        }
        
        return resultList;
    }

    public List<String> supportCategoryName = Arrays.asList("控制臂", "轮毂法兰盘", "散热器", "大灯总成", "雾灯总成", "尾灯总成", "空调压缩机", "空气压缩机", "翻新空调压缩机", "全新空调压缩机", "转向器", "翻新起动机", "全新起动机", "翻新发电机", "全新发电机", "减震器总成", "轮毂轴承");

    /**
     * 只处理部分品类的链接
     * @param asinGoodsHeads
     * @return
     */
    private boolean isSupportDelete(List<GoodsHead> asinGoodsHeads) {
        List<String> pdmGoodsCodes = asinGoodsHeads.stream().map(GoodsHead::getPdmGoodsCode).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(pdmGoodsCodes)) {
            return false;
        }

        List<GoodsDetailDTO> goodsDetail = pdmHttpRequestBiz.getGoodsDetail(pdmGoodsCodes);
        Map<String, GoodsDetailDTO> goodsDetailMap = goodsDetail.stream().collect(Collectors.toMap(GoodsDetailDTO::getGoodsCode, goods -> goods, (k1, k2) -> k1));

        for (GoodsHead goodsHead : asinGoodsHeads) {
            GoodsDetailDTO goodsDetailDTO = goodsDetailMap.get(goodsHead.getPdmGoodsCode());
            if (goodsDetailDTO == null) {
                return false;
            }
            if (!supportCategoryName.contains(goodsDetailDTO.getProductCategoryName())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 对链接按规则判断是否可以删除
     *
     * @param asin
     * @param vcInventoryMap
     * @param dataBatch
     */
    private void generateDeleteListingMonitor(String asin, List<GoodsHead> asinGoodsHeads, Map<String, BigDecimal> vcInventoryMap, String dataBatch, Integer type) {
        // SC链接
        List<GoodsHead> scList = asinGoodsHeads.stream().filter(g -> Arrays.asList(PublishType.FBA.getType(), PublishType.FBM.getType()).contains(g.getPublishType())).collect(Collectors.toList());

        // VC链接
        List<GoodsHead> vcList = asinGoodsHeads.stream().filter(g -> Arrays.asList(PublishType.VCDF.getType(), PublishType.VCPO.getType()).contains(g.getPublishType())).collect(Collectors.toList());

        // 获取SC链接FBA库存数据，FBA库存需要按平台SKU维度查询
        Map<String, BigDecimal> inventoryMap = getFbaInventoryMap(scList);

        // 待删除的链接ID
        List<Integer> waitingDeleteIds = Lists.newArrayList();
        // 先获取上架时间最早的一条，作为被跟卖的链接
        GoodsHead baseGoodsHead = asinGoodsHeads.stream().min(Comparator.comparing(GoodsHead::getOnlineTime)).orElse(null);
        // 存储每条listing的判断是否可以删除的结果
        Map<Integer, Map<String, Object>> resultMap = Maps.newHashMap();
        // 时间最早的链接作为主链接
        boolean mainGoodsHeadCanDelete = canDelete(baseGoodsHead, inventoryMap, vcInventoryMap, resultMap);
        // 主链接可以删除
        if (mainGoodsHeadCanDelete) {
            // 主链接加入待删除列表
            waitingDeleteIds.add(baseGoodsHead.getId());
        }
        // 仅一条链接，直接按  mainGoodsHeadCanDelete 删除
        if (asinGoodsHeads.size() == 1) {
            if (mainGoodsHeadCanDelete) {
                // 生成删除待办
                generateDeleteTodo(waitingDeleteIds, dataBatch, type);
            }
            return;
        }

        // 对跟卖关系判断
        List<GoodsHead> waitHandleScListing = scList.stream().filter(g -> !g.getId().equals(baseGoodsHead.getId())).collect(Collectors.toList());
        List<GoodsHead> waitHandleVcListing = vcList.stream().filter(g -> !g.getId().equals(baseGoodsHead.getId())).collect(Collectors.toList());
        // 先通过库存判断是否可以删除
        canDelete(vcInventoryMap, waitHandleScListing, inventoryMap, resultMap, waitHandleVcListing);

        // 首先VCPO链接是否有库存（一个ASIN最多一个VCPO链接和一个VCDF链接，FBA存在不同店铺跟卖同一个ASIN，会存在多个FBA链接和多个FBM链接）
        GoodsHead vcPoGoodsHead = waitHandleVcListing.stream().filter(g -> PublishType.VCPO.getType().equals(g.getPublishType())).findFirst().orElse(null);
        // VCDF链接
        GoodsHead vcDfGoodsHead = waitHandleVcListing.stream().filter(g -> PublishType.VCDF.getType().equals(g.getPublishType())).findFirst().orElse(null);

        // VCPO有库存
        if (vcPoGoodsHead != null && resultMap.get(vcPoGoodsHead.getId()).containsKey("hasVCPOStock") && (boolean) resultMap.get(vcPoGoodsHead.getId()).get("hasVCPOStock")) {
            // 当前VCPO一定是跟卖的主链接，不能删除主链接
            waitingDeleteIds.remove(baseGoodsHead.getId());
            // VCDF链接在VCPO链接上架时间之后，可以删除(若在FBA有库存链接的上架时间之前，可能被FBA跟卖，不能删除)
            if (vcDfGoodsHead != null && vcDfGoodsHead.getOnlineTime().after(vcPoGoodsHead.getOnlineTime())) {
                waitingDeleteIds.add(vcDfGoodsHead.getId());
            }

            // 可以删除其他SC链接，但是上架时间必须在当前VCPO链接上架时间之后的SC链接（在VCPO链接上架时间之前的SC链接，可能是VCPO链接的被跟卖链接，不能删除）
            removeScListing(waitHandleScListing, vcPoGoodsHead, resultMap, waitingDeleteIds, vcDfGoodsHead);
        }
        // VCPO没有库存
        else {
            // 判断SC FBA链接是否有库存
            List<GoodsHead> fbaHasStockListing = waitHandleScListing.stream().filter(g -> PublishType.FBA.getType().equals(g.getPublishType()) && resultMap.get(g.getId()).containsKey("hasFBAStock") && (boolean) resultMap.get(g.getId()).get("hasFBAStock")).collect(Collectors.toList());
            // SC FBA有库存
            if (CollUtil.isNotEmpty(fbaHasStockListing)) {
                // FBA跟卖的主链接，不能删除主链接
                waitingDeleteIds.remove(baseGoodsHead.getId());
                // 在FBA有库存链接的最近一条上架时间
                Date fbaOnlineTime = fbaHasStockListing.stream().max(Comparator.comparing(GoodsHead::getOnlineTime)).get().getOnlineTime();
                // VCPO的链接在FBA链接上架时间之后，可以删除
                if (vcPoGoodsHead != null && vcPoGoodsHead.getOnlineTime().after(fbaOnlineTime)) {
                    waitingDeleteIds.add(vcPoGoodsHead.getId());
                }
                // VCDF的链接在FBA链接上架时间之后，可以删除
                if (vcDfGoodsHead != null && vcDfGoodsHead.getOnlineTime().after(fbaOnlineTime)) {
                    waitingDeleteIds.add(vcDfGoodsHead.getId());
                }
                // 在FBA链接上架时间之后的SC链接，可以删除
                List<GoodsHead> scAfterFbaList = waitHandleScListing.stream().filter(g -> g.getOnlineTime().after(fbaOnlineTime)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(scAfterFbaList)) {
                    waitingDeleteIds.addAll(scAfterFbaList.stream().map(GoodsHead::getId).collect(Collectors.toList()));
                }
            }
            // VCPO 和 SC FBA 都没有库存
            else {
                // 所有链接都删除
                if (CollUtil.isNotEmpty(waitHandleScListing)) {
                    waitingDeleteIds.addAll(waitHandleScListing.stream().map(GoodsHead::getId).collect(Collectors.toList()));
                }
                if (CollUtil.isNotEmpty(waitHandleVcListing)) {
                    waitingDeleteIds.addAll(waitHandleVcListing.stream().map(GoodsHead::getId).collect(Collectors.toList()));
                }
            }
        }

        if (CollUtil.isNotEmpty(waitingDeleteIds)) {
            generateDeleteTodo(waitingDeleteIds, dataBatch, type);
        }
    }

    private void canDelete(Map<String, BigDecimal> vcInventoryMap, List<GoodsHead> waitHandleScListing, Map<String, BigDecimal> inventoryMap, Map<Integer, Map<String, Object>> resultMap, List<GoodsHead> waitHandleVcListing) {
        for (GoodsHead goodsHead : waitHandleScListing) {
            canDelete(goodsHead, inventoryMap, vcInventoryMap, resultMap);
        }
        for (GoodsHead goodsHead : waitHandleVcListing) {
            canDelete(goodsHead, inventoryMap, vcInventoryMap, resultMap);
        }
    }

    private void removeScListing(List<GoodsHead> waitHandleScListing, GoodsHead vcPoGoodsHead, Map<Integer, Map<String, Object>> resultMap, List<Integer> waitingDeleteIds, GoodsHead vcDfGoodsHead) {
        if (CollUtil.isEmpty(waitHandleScListing)) {
            return;
        }

        // 找到在当前VCPO链接上架时间之后的SC链接，根据FBA过滤掉FBA链接后，删除FBM链接
        List<GoodsHead> scAfterVcPoList = waitHandleScListing.stream().filter(g -> g.getOnlineTime().after(vcPoGoodsHead.getOnlineTime())).collect(Collectors.toList());
        if (CollUtil.isEmpty(scAfterVcPoList)) {
            return;
        }

        // 获取FBA链接
        List<GoodsHead> fbaListing = scAfterVcPoList.stream().filter(g -> PublishType.FBA.getType().equals(g.getPublishType()) && resultMap.get(g.getId()).containsKey("hasFBAStock") && (boolean) resultMap.get(g.getId()).get("hasFBAStock")).collect(Collectors.toList());
        // 存在FBA链接有库存的链接
        if (CollUtil.isNotEmpty(fbaListing)) {
            // FBA链接的最近一条上架时间
            Date fbaOnlineTime = fbaListing.stream().max(Comparator.comparing(GoodsHead::getOnlineTime)).get().getOnlineTime();
            // VCDF链接在FBA链接上架时间之前，可能被FBA跟卖，不能删除
            if (vcDfGoodsHead != null && vcDfGoodsHead.getOnlineTime().before(fbaOnlineTime)) {
                waitingDeleteIds.remove(vcDfGoodsHead.getId());
            }
            // 在FBA链接上架时间之后的SC链接，删除
            List<GoodsHead> scAfterFbaList = scAfterVcPoList.stream().filter(g -> g.getOnlineTime().after(fbaOnlineTime)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(scAfterFbaList)) {
                waitingDeleteIds.addAll(scAfterFbaList.stream().map(GoodsHead::getId).collect(Collectors.toList()));
            }
        }
        // 没有一条FBA链接有库存，删除所有SC链接
        else {
            if (CollUtil.isNotEmpty(scAfterVcPoList)) {
                waitingDeleteIds.addAll(scAfterVcPoList.stream().map(GoodsHead::getId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 生成删除待办
     *
     * @param waitingDeleteIds
     * @param dataBatch
     */
    private void generateDeleteTodo(List<Integer> waitingDeleteIds, String dataBatch, Integer type) {
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadByIds(waitingDeleteIds.toArray(new Integer[0]));
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }
        List<String> goodsCodes = goodsHeads.stream().map(GoodsHead::getPdmGoodsCode).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, GoodsDetailDTO> goodsDetailMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(goodsCodes)) {
            List<GoodsDetailDTO> goodsDetailList = pdmHttpRequestBiz.getGoodsDetail(goodsCodes);
            goodsDetailMap = goodsDetailList.stream().collect(Collectors.toMap(GoodsDetailDTO::getGoodsCode, Function.identity(), (k1, k2) -> k1));
        }

        for (GoodsHead goodsHead : goodsHeads) {
            AmListingDeleteMonitoringTodo todo = new AmListingDeleteMonitoringTodo();
            todo.setHeadId(goodsHead.getId().longValue());
            todo.setDataBatch(dataBatch);
            todo.setFromBrandLimitFlag(type.equals(2) ? 1 : 0);
            todo.setFromInvalidFlag(type.equals(1) ? 1 : 0);
            todo.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            todo.setShopCode(goodsHead.getShopCode());
            todo.setPlatformSku(goodsHead.getPlatformGoodsCode());
            todo.setAsin(goodsHead.getPlatformGoodsId());
            todo.setPublishType(goodsHead.getPublishType());
            todo.setCategoryId(goodsHead.getCategoryId()+"");
            GoodsDetailDTO goodsDetailDTO = MapUtil.isEmpty(goodsDetailMap) ? null : goodsDetailMap.get(goodsHead.getPdmGoodsCode());
            if (goodsDetailDTO == null) {
                continue;
            }
            if(!"2".equals(goodsDetailDTO.getClassificationCode())){
                continue;
            }

            if (ObjUtil.isNotEmpty(goodsDetailDTO)) {
                todo.setProductCategoryCode(Long.valueOf(goodsDetailDTO.getProductCategoryCode()));
                todo.setProductCategoryName(goodsDetailDTO.getProductCategoryName());
            }
            todo.setOperator(goodsHead.getCreateBy());
            todo.setStatus("2");
            todo.setCreateBy(goodsHead.getCreateBy());
            todo.setCreateTime(new Date());
            todo.setUpdateBy(goodsHead.getCreateBy());
            todo.setUpdateTime(new Date());
            todo.setOnlineTime(goodsHead.getOnlineTime());
            amListingDeleteMonitoringTodoService.insertOnDuplicateUpdate(todo);
        }

    }

    /**
     * 获取VC库存数据
     * 注意：这里的库存数据是按照ASIN维度
     * @param asinList
     * @return
     */
    private Map<String, BigDecimal> getVcPoInventoryMap(List<String> asinList) {
        List<VcInventorySnapshot> vcInventorySnapshots = odsCrlCrlVcCatalogDataService.listVCPOInventory(asinList);
        Map<String, BigDecimal> vcInventoryMap = vcInventorySnapshots.stream().collect(Collectors.groupingBy(VcInventorySnapshot::getPlatformSaleCode,
                Collectors.reducing(BigDecimal.ZERO, e -> Convert.toBigDecimal(e.getSellableOnHandUnits(),BigDecimal.ZERO)
                        .add(Convert.toBigDecimal(e.getOpenPurchaseOrderQuantity(), BigDecimal.ZERO)), BigDecimal::add)));
        return vcInventoryMap;
    }

    /**
     * 获取SC链接库存数据
     * 注意：这里的库存数据是按照平台SKU维度
     * @param scList
     * @return
     */
    private Map<String, BigDecimal> getFbaInventoryMap(List<GoodsHead> scList) {
        if (CollUtil.isEmpty(scList)) {
            return Maps.newHashMap();
        }
        scList = scList.stream().filter(g -> PublishType.FBA.getType().equals(g.getPublishType())).collect(Collectors.toList());
        if (CollUtil.isEmpty(scList)) {
            return Maps.newHashMap();
        }
        List<GetFbmStockInvRequestDTO> requestList = Lists.newArrayList();
        for (GoodsHead goodsHead : scList) {
            GetFbmStockInvRequestDTO requestDTO = buildInvRequestDTO(goodsHead);
            requestList.add(requestDTO);
        }

        List<ThirdpartyFbmDTO> fbaStockList = thirdpartyInventoryBiz.getFbaStock(requestList);
        // 按平台SKU维度汇总，可售库存+在途库存, 汇总到SKU维度
        Map<String, BigDecimal> inventoryMap = fbaStockList.stream().collect(Collectors.groupingBy(ThirdpartyFbmDTO::getWarehouseSku,
                Collectors.reducing(BigDecimal.ZERO, e -> Convert.toBigDecimal(e.getSellableQty(),BigDecimal.ZERO).add(Convert.toBigDecimal(e.getOnwayQty(), BigDecimal.ZERO)), BigDecimal::add)));
        return inventoryMap;
    }

    private GetFbmStockInvRequestDTO buildInvRequestDTO(GoodsHead goodsHead) {
        GetFbmStockInvRequestDTO requestDTO = new GetFbmStockInvRequestDTO();
        requestDTO.setWarehouseSku(goodsHead.getPlatformGoodsCode());
        requestDTO.setIsContainFba(true);
        requestDTO.setWarehouseType("FBA");
        return requestDTO;
    }

    private boolean canDelete(GoodsHead goodsHead, Map<String, BigDecimal> inventoryMap, Map<String, BigDecimal> vcInventoryMap, Map<Integer, Map<String, Object>> resultMap) {
        boolean res = true;

        if (PublishType.FBA.getType().equals(goodsHead.getPublishType())) {
            // FBA
            res = canDeleteFba(goodsHead, inventoryMap);
            resultMap.computeIfAbsent(goodsHead.getId(), k -> Maps.newHashMap()).put("hasFBAStock", !res);
        } else if (PublishType.VCPO.getType().equals(goodsHead.getPublishType())) {
            // VCPO
            res = canDeleteVcPo(goodsHead, vcInventoryMap);
            resultMap.computeIfAbsent(goodsHead.getId(), k -> Maps.newHashMap()).put("hasVCPOStock", !res);
        }
        resultMap.computeIfAbsent(goodsHead.getId(), k -> Maps.newHashMap()).put("canDelete", res);
        return res;
    }

    /**
     * 判断VCPO是否可以删除，ASIN维度，有在售和在途库存，无法删除
     * dws_stock_inventory_sales_vc_snapshot
     *
     * @param goodsHead
     * @param vcInventoryMap
     */
    private boolean canDeleteVcPo(GoodsHead goodsHead, Map<String, BigDecimal> vcInventoryMap) {
        if (vcInventoryMap == null || vcInventoryMap.isEmpty()) {
            return true;
        }
        BigDecimal inventory = vcInventoryMap.get(goodsHead.getPlatformGoodsId());
        return inventory == null || inventory.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 判断FBA是否可以删除, 平台SKU维度，有在售和在途库存，无法删除
     * dws_stock_product_inventory_snapshot
     *
     * @param goodsHead
     * @param inventoryMap
     */
    private boolean canDeleteFba(GoodsHead goodsHead, Map<String, BigDecimal> inventoryMap) {
        if (inventoryMap == null || inventoryMap.isEmpty()) {
            return true;
        }
        BigDecimal inventory = inventoryMap.get(goodsHead.getPlatformGoodsCode());
        return inventory == null || inventory.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 填充VC上架时间
     * @param asin
     * @param vcAsinMap
     * @param vcList
     */
    private static void fillVCOnlineTime(String asin, Map<String, List<OdsCrlCrlVcCatalogData>> vcAsinMap, List<GoodsHead> vcList) {
        List<OdsCrlCrlVcCatalogData> odsCrlCrlVcCatalogData = vcAsinMap.get(asin);
        Map<String,OdsCrlCrlVcCatalogData> vcCatalogDataMap = odsCrlCrlVcCatalogData.stream().collect(Collectors.toMap(OdsCrlCrlVcCatalogData::getVendorCode, Function.identity()));
        if (CollUtil.isNotEmpty(odsCrlCrlVcCatalogData)) {
            for (GoodsHead goodsHead : vcList) {
                if (PublishType.VCDF.getType().equals(goodsHead.getPublishType())) {
                    OdsCrlCrlVcCatalogData vcDf = vcCatalogDataMap.get("WM741");
                    if (vcDf != null) {
                        goodsHead.setOnlineTime(vcDf.getCreateTime());
                    }
                }else if (PublishType.VCPO.getType().equals(goodsHead.getPublishType())) {
                    OdsCrlCrlVcCatalogData vcPo = vcCatalogDataMap.get("IH75B");
                    if (vcPo != null) {
                        goodsHead.setOnlineTime(vcPo.getCreateTime());
                    }
                }
            }
        }
    }

    /**
     * 移除有销量、评论、星级大于4的ASIN
     *
     * @param asinList
     * @param type
     */
    private List<String> removeAsinBySalesReview(List<String> asinList,Integer type) {
        List<String> resultList = Lists.newArrayList();
        if (CollUtil.isEmpty(asinList)) {
            return resultList;
        }
        int month = type == 1 ? 9 : 3;
        //查询listing销量表
        List<Map<String, String>> autoSalesDetails = odsCrlCrlVcCatalogDataService.listAutoSalesDetailByDate(asinList, month);
        Map<String, BigDecimal> salesDetailMap = autoSalesDetails.stream().collect(Collectors.toMap(e -> e.get("platform_sale_code"),
                e -> Convert.toBigDecimal(e.get("sales_volume"), BigDecimal.ZERO)));

        List<ReviewStar> reviewStars = odsCrlCrlVcCatalogDataService.listReviewByAsinList(asinList);
        Map<String, ReviewStar> reviewStarMap = reviewStars.stream().collect(Collectors.toMap(ReviewStar::getPlatformSaleCode, Function.identity()));

        for (String asin : asinList) {
            // 无销量、评论、星级大于4，需保留
            if (!hasSalesReview(asin, salesDetailMap, reviewStarMap)) {
                resultList.add(asin);
            }
        }
        return resultList;
    }

    /**
     * 判断是否有销量、评论、星级大于4
     * @param asin
     * @param salesDetailMap
     * @param reviewStarMap
     * @return
     */
    private boolean hasSalesReview(String asin, Map<String, BigDecimal> salesDetailMap, Map<String, ReviewStar> reviewStarMap) {
        if (salesDetailMap.containsKey(asin) ) {
            BigDecimal salesVolume270 = salesDetailMap.get(asin);
            if (salesVolume270 != null && salesVolume270.compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }
        ReviewStar reviewStar = reviewStarMap.get(asin);
        if (reviewStar != null) {
            // 有评论，需过滤
            if (reviewStar.getCommentsQty() != null && reviewStar.getCommentsQty().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
            // 星级大于等于4，需过滤
            return reviewStar.getStarAvg() != null && reviewStar.getStarAvg().compareTo(new BigDecimal("4")) >= 0;
        }
        return false;
    }

    /**
     * 插入VC ASIN
     */
    private void insertVcAsinList() {
        Integer minId = 0;
        Integer maxId = odsCrlCrlVcCatalogDataService.selectMaxIdBefore270Days();
        List<OdsCrlCrlVcCatalogData> vcCatalogDataList = odsCrlCrlVcCatalogDataService.selectVcAsinList(minId, maxId);
        if (CollUtil.isEmpty(vcCatalogDataList)) {
            return;
        }
        List<String> asinList = vcCatalogDataList.stream().map(OdsCrlCrlVcCatalogData::getAsin).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(asinList)) {
            return;
        }
        // 插入临时表
        goodsHeadService.insertScAsinList(asinList);
        while (minId < maxId) {
            minId = vcCatalogDataList.stream().map(OdsCrlCrlVcCatalogData::getId).max(Integer::compareTo).orElse(0);
            if (minId >= maxId) {
                break;
            }
            vcCatalogDataList = odsCrlCrlVcCatalogDataService.selectVcAsinList(minId, maxId);
            if (CollUtil.isEmpty(vcCatalogDataList)) {
                break;
            }
            asinList = vcCatalogDataList.stream().map(OdsCrlCrlVcCatalogData::getAsin).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(asinList)) {
                break;
            }
            goodsHeadService.insertScAsinList(asinList);
        }
    }

    /**
     * 插入SC ASIN
     */
    private void insertScAsinList() {
        Integer minId = 0;
        Integer maxId = goodsHeadService.selectMaxIdBefore270Days();
        List<GoodsHead> goodsHeads = goodsHeadService.selectScAsinList(minId, maxId);
        if (CollUtil.isEmpty(goodsHeads)) {
            return;
        }
        List<String> asinList = goodsHeads.stream().map(GoodsHead::getPlatformGoodsId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(asinList)) {
            return;
        }
        // 插入临时表
        goodsHeadService.insertScAsinList(asinList);
        while (minId < maxId) {
            minId = goodsHeads.stream().map(GoodsHead::getId).max(Integer::compareTo).orElse(0);
            if (minId >= maxId) {
                break;
            }
            goodsHeads = goodsHeadService.selectScAsinList(minId, maxId);
            if (CollUtil.isEmpty(goodsHeads)) {
                break;
            }
            asinList = goodsHeads.stream().map(GoodsHead::getPlatformGoodsId).distinct().collect(Collectors.toList());
            if (CollUtil.isEmpty(asinList)) {
                break;
            }
            // 插入临时表
            goodsHeadService.insertScAsinList(asinList);
        }
    }

    public void doGenerateAmazonDeleteListing() {
        Date before270 = DateUtils.addDays(new Date(), -270);
        String dataBatch = DateUtil.format(DateUtil.date(), "yyyyMMdd");

        // 获取ASIN
        // 改成do while
        List<String> asinList = goodsHeadService.listTempAsinList();
        while (CollUtil.isNotEmpty(asinList)) {
            handleAsin(asinList, before270,dataBatch, 1, null);
            asinList = goodsHeadService.listTempAsinList();
        }
    }

    /**
     * 判断是否可刊登
     *
     * @param brand          品牌
     * @param goodsCode      商品编码
     * @param goodsDetailMap 商品详情
     * @param shopCode
     */
    public void canPutListing(String brand, String goodsCode, Map<String,GoodsDetailDTO> goodsDetailMap, String shopCode) {
        String logMsg = StrUtil.format("品牌：{}，商品编码：{}", brand, goodsCode);
        if(StrUtil.isBlank(brand) || StrUtil.isBlank(goodsCode)) {
            log.error(logMsg + "，品牌或商品编码为空，无法判断是否可刊登");
            return;
        }
        // 当前店铺是否可以刊登当前品牌
        canPutListingByShopAndBrand(brand, goodsCode, logMsg, shopCode);

        // 当前品牌是否可以使用当前品类进行刊登
        canPutListingByBrandAndCategory(brand, goodsCode, goodsDetailMap, logMsg);
    }

    private void canPutListingByShopAndBrand(String brand, String goodsCode, String logMsg, String shopCode) {
        logMsg += "，店铺编码：" + shopCode;
        // 查询店铺限制配置
        AmShopLimitConfig amShopLimitConfig = amShopLimitConfigService.getAmShopLimitConfig(shopCode);
        if (amShopLimitConfig == null) {
            log.info(logMsg + "，店铺限制配置为空，默认可刊登");
            return;
        }
        String allowPutBrand = amShopLimitConfig.getAllowPutBrand();
        if (StrUtil.isBlank(allowPutBrand)) {
            log.info(logMsg + "，店铺限制配置的值为空，默认可刊登");
            return;
        }
        if("all".equalsIgnoreCase(allowPutBrand)) {
            log.info(logMsg + "，店铺限制配置为all，默认可刊登");
            return;
        }
        if("none".equalsIgnoreCase(allowPutBrand)) {
            log.info(logMsg + "，店铺限制配置为none，不可刊登");
            throw new BusinessException("[店铺品牌限制]当前品牌" + brand + "不可刊登，请更换品牌");
        }
        String[] allowPutBrandArray = allowPutBrand.split(",");
        for (String allowPutBrandItem : allowPutBrandArray) {
            if (brand.equalsIgnoreCase(allowPutBrandItem)) {
                log.info(logMsg + "，当前品牌" + brand + "可刊登");
                return;
            }
        }
        log.info(logMsg + "，当前品牌" + brand + "不可刊登，请更换品牌");
        throw new BusinessException("[店铺品牌限制]当前品牌" + brand + "不可刊登，请更换品牌");
    }

    private void canPutListingByBrandAndCategory(String brand, String goodsCode, Map<String, GoodsDetailDTO> goodsDetailMap, String logMsg) {
        // 查询品牌限制配置
        AmBrandLimitConfig amBrandLimitConfig = amBrandLimitConfigService.getAmBrandLimitConfig(brand);
        if (amBrandLimitConfig == null) {
            log.info(logMsg + "，品牌限制配置为空，默认可刊登");
            return;
        }
        String allowPutCategory = amBrandLimitConfig.getAllowPutCategory();
        if (StrUtil.isBlank(allowPutCategory)) {
            log.info(logMsg + "，品类限制配置的值为空，默认可刊登");
            return;
        }
        if("all".equalsIgnoreCase(allowPutCategory)) {
            log.info(logMsg + "，品类限制配置为all，默认可刊登");
            return;
        }
        if("none".equalsIgnoreCase(allowPutCategory)) {
            log.info(logMsg + "，品类限制配置为none，不可刊登");
            throw new BusinessException("[品牌品类限制]当前品牌" + brand + "不可刊登，请更换品牌");
        }
        if (goodsDetailMap.containsKey(goodsCode)) {
            GoodsDetailDTO goodsDetailDTO = goodsDetailMap.get(goodsCode);
            if (goodsDetailDTO == null) {
                log.info(logMsg + "，商品编码：{}，商品详情为空", goodsCode);
                throw new BusinessException("当前商品编码" + goodsCode + "不存在，请检查商品编码是否正确");
            }

            if(!"2".equals(goodsDetailDTO.getClassificationCode())){
                log.info(logMsg + "，商品编码：{}，商品不是配件", goodsCode);
                return;
            }

            String category = goodsDetailDTO.getProductCategoryName();
            checkCategory(brand, goodsCode, allowPutCategory, category, logMsg);
        }else {
            List<GoodsDetailDTO> goodsDetailDTOList = pdmHttpRequestBiz.getGoodsDetail(CollUtil.newArrayList(goodsCode));
            if (CollUtil.isEmpty(goodsDetailDTOList)) {
                goodsDetailMap.put(goodsCode, null);
                throw new BusinessException("[品牌品类限制]当前商品编码" + goodsCode + "不存在，请检查商品编码是否正确");
            }
            GoodsDetailDTO goodsDetailDTO = goodsDetailDTOList.get(0);
            if(!"2".equals(goodsDetailDTO.getClassificationCode())){
                log.info(logMsg + "，商品编码：{}，商品不是配件", goodsCode);
                return;
            }
            goodsDetailMap.put(goodsCode, goodsDetailDTO);

            String category = goodsDetailDTO.getProductCategoryName();
            checkCategory(brand, goodsCode, allowPutCategory, category, logMsg);
        }
    }

    private void checkCategory(String brand, String goodsCode, String allowPutCategory, String category, String logMsg) {
        if (canPutCategory(allowPutCategory, category)) {
            log.info(logMsg + "，商品编码：{}，品类：{}，可刊登", goodsCode, category);
        }else {
            log.info(logMsg + "，商品编码：{}，品类：{}，不可刊登", goodsCode, category);
            throw new BusinessException("[品牌品类限制]当前品牌" + brand + "，品类：" + category + "，限制刊登，请更换品牌或商品编码");
        }
    }

    private boolean canPutCategory(String allowPutCategory, String category) {
        String[] allowPutCategoryArray = allowPutCategory.split(",");
        for (String allowPutCategoryItem : allowPutCategoryArray) {
            if (category.equalsIgnoreCase(allowPutCategoryItem)) {
                return true;
            }
        }
        return false;
    }

    public void autoDeleteListingByAmazon(String type) {
        if (StrUtil.isBlank(type)) {
            log.error("type不能为空");
            return;
        }
        log.info("开始删除链接");
        String maxDataBatch = amListingDeleteMonitoringTodoService.selectMaxDataBatch();
        if(StrUtil.isBlank(maxDataBatch)) {
            log.info("没有数据需要删除");
            return;
        }

        int lastId = 0;
        int fromInvalidFlag = 0;    
        int fromBrandLimitFlag = 0;
        if ("1".equals(type)) {
            fromInvalidFlag = 1;
        }else if("2".equals(type)) {
            fromBrandLimitFlag = 1;
        }
        List<AmListingDeleteMonitoringTodo> amListingDeleteMonitoringTodos = amListingDeleteMonitoringTodoService.listNeedDelete(maxDataBatch, fromInvalidFlag, fromBrandLimitFlag, lastId);
        if(CollUtil.isEmpty(amListingDeleteMonitoringTodos)) {
            log.info("没有数据需要删除");
            return;
        }
        while (CollUtil.isNotEmpty(amListingDeleteMonitoringTodos)) {   
            List<Long> headIds = amListingDeleteMonitoringTodos.stream().map(AmListingDeleteMonitoringTodo::getHeadId).collect(Collectors.toList());
            if (CollUtil.isEmpty(headIds)) {
                log.info("没有数据需要删除");
                return;
            }
            List<List<Long>> partition = Lists.partition(headIds, 200);
            for (List<Long> ids : partition) {
                deleteGoodsHeadByIds(ids);
            }
            List<Long> ids = amListingDeleteMonitoringTodos.stream().map(AmListingDeleteMonitoringTodo::getId).collect(Collectors.toList());
            amListingDeleteMonitoringTodoService.deleteAmListingDeleteMonitoringTodoByIds(ids.stream().map(String::valueOf).collect(Collectors.joining(",")));
            lastId = amListingDeleteMonitoringTodos.get(amListingDeleteMonitoringTodos.size() - 1).getId().intValue();
            amListingDeleteMonitoringTodos = amListingDeleteMonitoringTodoService.listNeedDelete(maxDataBatch, fromInvalidFlag, fromBrandLimitFlag, lastId);
        }
    }
    
    private void deleteGoodsHeadByIds(List<Long> headIds) {
        if (CollUtil.isEmpty(headIds)) {
            return;
        }
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(headIds.stream().map(Long::intValue).toArray(Integer[]::new));
        if (CollUtil.isEmpty(goodsHeadList)) {
            return;
        }

        Iterator<GoodsHead> iterator = goodsHeadList.iterator();
        while (iterator.hasNext()) {
            GoodsHead goodsHead = iterator.next();
            try {
                // 校验商品状态是否可以删除
                PublishStatus.checkListingByStatusAndPlatform(goodsHead.getPublishStatus(), goodsHead.getPlatform());
            }catch (Exception e) {
               log.error("商品状态校验失败，商品id：{}，商品编码：{}", goodsHead.getId(), goodsHead.getPdmGoodsCode());
               iterator.remove();
            }
        }

       
        Map<String, List<GoodsHead>> plafromMap = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getPlatform));

        for (String platformType : plafromMap.keySet()) {
            List<GoodsHead> heads = plafromMap.get(platformType);
            List<Integer> list = heads.stream().map(GoodsHead::getId).collect(Collectors.toList());
            goodsTaskService.insertGoodsPendingProcessingTask(platformType, GoodsTaskTypeEnum.BATCH_DELETE, list, "-1");

            IBaseListingService listingServiceByPlatformType = platformListingFactory.getListingServiceByPlatformType(platformType);
            Integer integer = listingServiceByPlatformType.deleteListing(heads);
        }

        // 删除商品记录日志
        for (Long id : headIds) {
            ListingLog listingLog = new ListingLog();
            listingLog.setStatus(0);
            listingLog.setDetails("系统自动删除无效链接");
            listingLog.setListingId(id.intValue());
            listingLog.setOperName("-1");
            listingLog.setOperTime(new Date());
            listingLogService.insertListingLog(listingLog);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddMonitor(String goodsCodes, String listingIds) {
        if (StrUtil.isNotBlank(goodsCodes)) {
            // 批量加入竞对监控
            String[] goodsCodeArray = goodsCodes.split(",");
            addCompetitorMonitor(goodsCodeArray);
        }
        if (StrUtil.isNotBlank(listingIds)) {
            // 批量加入链接监控
            String[] listingIdArray = listingIds.split(",");
            addLinkMonitor(listingIdArray);
        }
    }

    private void addCompetitorMonitor(String[] goodsCodeArray) {
        // 批量加入竞对监控
        for (String goodsCode : goodsCodeArray) {
            if (StrUtil.isBlank(goodsCode)) {
                continue;
            }
            // 是否超过上限
            checkCompetitorMonitorLimit();
         
            AmMonitorPoolConfig query = new AmMonitorPoolConfig();
            query.setPdmGoodsCode(goodsCode);
            query.setType(1);
            List<AmMonitorPoolConfig> amMonitorPoolConfigList = amMonitorPoolConfigService.selectAmMonitorPoolConfigList(query);
            if (CollUtil.isNotEmpty(amMonitorPoolConfigList)) {
                continue;
            }
             
            // 查询商品信息
            GoodsDetailDTO goodsDetailDTO = pdmHttpRequestBiz.getGoodsDetail(goodsCode);
            if (goodsDetailDTO == null) {
                continue;
            }
            AmMonitorPoolConfig amMonitorPoolConfig = new AmMonitorPoolConfig();
            amMonitorPoolConfig.setPdmGoodsCode(goodsCode);
            amMonitorPoolConfig.setType(1);
            amMonitorPoolConfig.setStatus(0);
            amMonitorPoolConfig.setDelFlag(0);
            amMonitorPoolConfig.setCreateBy(ShiroUtils.getUserId()+"");
            amMonitorPoolConfigService.insertAmMonitorPoolConfig(amMonitorPoolConfig);
        }
    }

    /**
     * 检查竞品监控数量是否超过配置上限
     * 通过系统配置获取竞品监控上限和重点链接上限
     * 如果超过上限则抛出异常
     */
    private void checkCompetitorMonitorLimit() {
        // 获取竞品监控上限配置
        String competitorLimitStr = sysConfigService.selectConfigByKey("competitor_monitor_limit");
        if (StrUtil.isBlank(competitorLimitStr)) {
            return;
        }
        int competitorLimit = 0; 
        if (StrUtil.isNotBlank(competitorLimitStr)) {
            try {
                competitorLimit = Integer.parseInt(competitorLimitStr);
            } catch (NumberFormatException e) {
                log.error("竞品监控上限配置格式错误: {}", competitorLimitStr);
            }
        }

        // 查询当前竞品监控数量
        int currentCount = amMonitorPoolConfigService.countAmMonitorPoolConfig(1, 0);
        
        // 如果当前数量已达到或超过上限，则抛出异常
        if (competitorLimit > 0 && currentCount >= competitorLimit) {
            throw new BusinessException("竞品监控数量已达到上限" + competitorLimit + "，请删除部分监控后再添加");
        }
    }

    /**
     * 检查重点链接监控数量是否超过配置上限
     * 通过系统配置获取重点链接上限
     * 如果超过上限则抛出异常
     */
    private void checkLinkMonitorLimit() {
        // 获取重点链接监控上限配置
        String linkLimitStr = sysConfigService.selectConfigByKey("link_monitor_limit");
        if (StrUtil.isBlank(linkLimitStr)) {
            return;
        }
        int linkLimit = 0; // 默认上限为200
        if (StrUtil.isNotBlank(linkLimitStr)) {
            try {
                linkLimit = Integer.parseInt(linkLimitStr);
            } catch (NumberFormatException e) {
                log.error("重点链接监控上限配置格式错误: {}", linkLimitStr);
            }
        }

        // 查询当前重点链接监控数量
        int currentCount = amMonitorPoolConfigService.countAmMonitorPoolConfig(2, 0);
        
        // 如果当前数量已达到或超过上限，则抛出异常
        if (linkLimit > 0 && currentCount >= linkLimit) {
            throw new BusinessException("重点链接监控数量已达到上限" + linkLimit + "，请删除部分监控后再添加");
        }
    }

    private void addLinkMonitor(String[] listingIdArray) {
        Long userId = ShiroUtils.getUserId();
        // 批量加入链接监控
        for (String listingId : listingIdArray) {
            if (StrUtil.isBlank(listingId)) {
                continue;
            }
            // 检查是否超过重点链接监控上限
            checkLinkMonitorLimit();
            
            AmMonitorPoolConfig query = new AmMonitorPoolConfig();
            query.setHeadId(Long.parseLong(listingId));
            query.setType(2);
            List<AmMonitorPoolConfig> amMonitorPoolConfigList = amMonitorPoolConfigService.selectAmMonitorPoolConfigList(query);
            if (CollUtil.isNotEmpty(amMonitorPoolConfigList)) {
                continue;
            }
            GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(Integer.parseInt(listingId));
            if (goodsHead == null || StrUtil.isBlank(goodsHead.getPdmGoodsCode()) || StrUtil.isBlank(goodsHead.getPlatformGoodsId())) {
                continue;
            }
            AmMonitorPoolConfig amMonitorPoolConfig = new AmMonitorPoolConfig();
            amMonitorPoolConfig.setHeadId(Long.parseLong(listingId));
            amMonitorPoolConfig.setType(2);
            amMonitorPoolConfig.setStatus(0);
            amMonitorPoolConfig.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            amMonitorPoolConfig.setPlatformGoodsId(goodsHead.getPlatformGoodsId());
            amMonitorPoolConfig.setPlatformGoodsCode(goodsHead.getPlatformGoodsCode());
            amMonitorPoolConfig.setPublishType(goodsHead.getPublishType());
            amMonitorPoolConfig.setShopCode(goodsHead.getShopCode());
            amMonitorPoolConfig.setSite(goodsHead.getSiteCode());
            amMonitorPoolConfig.setDelFlag(0);
            amMonitorPoolConfig.setCreateBy(String.valueOf(userId));
            amMonitorPoolConfigService.insertAmMonitorPoolConfig(amMonitorPoolConfig);

            listingLogService.insertSuccessListingLog("批量加入链接监控,Listing加入重点链接监控,数据将在次日展示于首页-重点链接监控板块", String.valueOf(userId), Integer.valueOf(listingId));
        }
    }

    public void batchRemoveMonitor(String goodsCodes, String listingIds) {
        if (StrUtil.isNotBlank(goodsCodes)) {
            // 批量移除竞对监控
            String[] goodsCodeArray = goodsCodes.split(",");
            removeCompetitorMonitor(goodsCodeArray);
        }
        if (StrUtil.isNotBlank(listingIds)) {
            // 批量移除链接监控
            String[] listingIdArray = listingIds.split(",");
            removeLinkMonitor(listingIdArray);
        }
    }

    private void removeCompetitorMonitor(String[] goodsCodeArray) {
        for (String goodsCode : goodsCodeArray) {
            if (StrUtil.isBlank(goodsCode)) {
                continue;
            }
            AmMonitorPoolConfig query = new AmMonitorPoolConfig();
            query.setPdmGoodsCode(goodsCode);
            query.setType(1);
            List<AmMonitorPoolConfig> amMonitorPoolConfigList = amMonitorPoolConfigService.selectAmMonitorPoolConfigList(query);
            if (CollUtil.isEmpty(amMonitorPoolConfigList)) {
                continue;
            }
            for (AmMonitorPoolConfig amMonitorPoolConfig : amMonitorPoolConfigList) {
                amMonitorPoolConfig.setUpdateBy(ShiroUtils.getUserId()+"");
                amMonitorPoolConfig.setDelFlag(1);
                amMonitorPoolConfigService.updateAmMonitorPoolConfig(amMonitorPoolConfig);
            }
        }
    }

    private void removeLinkMonitor(String[] listingIdArray) {
        for (String listingId : listingIdArray) {
            if (StrUtil.isBlank(listingId)) {
                continue;
            }
            AmMonitorPoolConfig query = new AmMonitorPoolConfig();
            query.setHeadId(Long.parseLong(listingId));
            query.setType(2);
            List<AmMonitorPoolConfig> amMonitorPoolConfigList = amMonitorPoolConfigService.selectAmMonitorPoolConfigList(query);
            if (CollUtil.isEmpty(amMonitorPoolConfigList)) {
                continue;
            }
            for (AmMonitorPoolConfig amMonitorPoolConfig : amMonitorPoolConfigList) {
                amMonitorPoolConfig.setUpdateBy(ShiroUtils.getUserId()+"");
                amMonitorPoolConfig.setDelFlag(1);
                amMonitorPoolConfigService.updateAmMonitorPoolConfig(amMonitorPoolConfig);
            }
        }

    }
    /**
     * 更新库存为0
     *
     * @param head
     */
    public void updateZeroStock(GoodsHead head, String type) {
        //如果是刊登中 更新中 下架中的数据 不允许修改
        if (PublishStatus.getNoUpdateStatus().contains(head.getPublishStatus())) {
            throw new RuntimeException("listing状态为刊登中、更新中、下架中、非在售的数据不允许修改");
        }
        GoodsHead updatedHead = new GoodsHead();
        updatedHead.setId(head.getId());
        updatedHead.setStockOnSalesQty(BigDecimal.ZERO);
        goodsHeadService.updateListingGoodsHead(updatedHead);
        if (ObjectUtil.isNotEmpty(head.getPlatformGoodsId())) {
            baseAmazonProductUpdateV2Task.updateInventoryAmazonV2(head, type);
        }
        listingLogService.insertSuccessListingLog("加入库存更新黑名单,库存由" + head.getStockOnSalesQty() + "更为0", head.getCreateBy(), head.getId());
    }

    /**
     * vc 更新库存为0
     *
     * @param goodList
     */
    public void updateVCZeroStock(List<GoodsHead> goodList, String shopCode, Long userId) {
        if (CollUtil.isNotEmpty(goodList)) {
            ConfigStoreInfo configStoreInfo = configStoreInfoService.selectConfigStoreInfoByShopCode(shopCode);
            if (ObjUtil.isEmpty(configStoreInfo)) {
                log.error("店铺配置不存在,VC更新库存为0失败!店铺编码:{}", shopCode);
            }
            // 日志内容
            Map<Integer, StringBuilder> goodsLogMap = new HashMap<>();

            List<AmazonWarehouseMapping> warehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode(shopCode);
            Map<String, String> whCodeMap = warehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, AmazonWarehouseMapping::getWhCode));


            List<Integer> goodsIds = goodList.stream().map(GoodsHead::getId).collect(Collectors.toList());
            List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(goodsIds.stream().toArray(Integer[]::new));
            if (ObjUtil.isEmpty(goodsHeadList)) {
                return;
            }
            whCodeMap.forEach((amWhCde, whCode) -> {
                List<VcListingInventory> vcInventoryUpdate = buildVcZeroInventoryUpdateByNoReport(goodsHeadList);
                vcInventoryUpdate.forEach(inventory -> {
                    StringBuilder logContent = goodsLogMap.computeIfAbsent(inventory.getGoodsId(), k -> new StringBuilder());
                    logContent.append("仓库编码：").append(whCode).append("，库存更新为：").append(inventory.getAvailableInventory()).append("；");
                });

                baseAmazonProductUpdateV2Task.updateInventoryAmazonVc(configStoreInfo, amWhCde, vcInventoryUpdate, null);
            });
            goodsLogMap.forEach((goodsId, logContent) -> {
                listingLogService.insertSuccessListingLog(logContent.toString(), userId + "", goodsId);
            });

        }

    }


    public List<ListingAmazonAttributeLineV2> getAmAttributeLinesV2(JSONObject attributes, GoodsHead goodsHead) {
        List<ListingAmazonAttributeLineV2> amazonAttributeLinesV2 = new ArrayList<>();

        Integer categoryId = goodsHead.getCategoryId();
        String vcFlag = goodsHead.getShopCode().contains("VC") ? "Y" : "N";

        //获取属性列表
        List<AmCategoryTemplateField> amCategoryTemplateFields = categoryInfoHandleBiz.getAmCategoryTemplateFields(goodsHead.getSiteCode(), categoryId, vcFlag);
        if (CollectionUtil.isEmpty(amCategoryTemplateFields)){
            return amazonAttributeLinesV2;
        }
        
        // 获取带属性组ID的多值属性映射 key:propNodePath  value:List<AttributeWithGroupInfo>
        Map<String, List<AttributeWithGroupInfo>> attributeMultiMapWithGroup = convertAttributeMapMultiWithGroupId(attributes,"");
        
        // 为兼容性保留原有的单值映射
        Map<String, String> attributeMap = convertAttributeMap(attributes,"");

        List<String> attributeV2Names = AmazonAttributeEnum.getEnumValuesByFlag(vcFlag);

        String productType ="";
        for (AmCategoryTemplateField field : amCategoryTemplateFields) {
            productType = field.getProductType();
            String propNodePath = field.getPropNodePath();
            String fieldName = field.getFieldName();
            
            // 如果既不在多值map也不在单值map中，则跳过
            if (!attributeMultiMapWithGroup.containsKey(propNodePath) && !attributeMap.containsKey(propNodePath)) {
                continue;
            }
            
            Integer tableType = attributeV2Names.contains(propNodePath) ? 4 : 0;
            
            // 处理多值情况
            if (attributeMultiMapWithGroup.containsKey(propNodePath) && attributeMultiMapWithGroup.get(propNodePath).size() > 1) {
                // 对于多值属性，创建多个ListingAmazonAttributeLineV2对象
                handleMultiValueAttributeWithGroupId(
                    goodsHead, 
                    categoryId, 
                    productType, 
                    propNodePath, 
                    fieldName, 
                    attributeMultiMapWithGroup.get(propNodePath), 
                    tableType, 
                    vcFlag, 
                    amazonAttributeLinesV2
                );
            } else {
                // 处理单值情况
                ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
                // attributeLineV2.setHeadId(Long.valueOf(goodsHead.getId()));
                attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
                attributeLineV2.setCategoryId(categoryId);
                attributeLineV2.setProductType(productType);
                attributeLineV2.setPropNodePath(propNodePath);
                attributeLineV2.setTableName(fieldName);
                
                // 设置属性组ID和值
                String propertyGroupId = AttributeGroupIdGenerator.generateDefaultGroupId();
                if (attributeMultiMapWithGroup.containsKey(propNodePath) && !attributeMultiMapWithGroup.get(propNodePath).isEmpty()) {
                    AttributeWithGroupInfo groupInfo = attributeMultiMapWithGroup.get(propNodePath).get(0);
                    attributeLineV2.setTableValue(groupInfo.getValue());
                    propertyGroupId = groupInfo.getGroupId();
                } else {
                    attributeLineV2.setTableValue(attributeMap.get(propNodePath));
                }
                
                // 兼容同步下来的数据不是在枚举项中
                if (ObjUtil.equals("required_product_compliance_certificate.value", propNodePath)) {
                    String value = attributeLineV2.getTableValue();
                    if (StrUtil.isNotEmpty(value) && value.contains("CARB")) {
                        attributeLineV2.setTableValue("California Air Review Board (CARB)");
                    } else {
                        attributeLineV2.setTableValue("Not Applicable");
                    }
                }

                attributeLineV2.setTableType(tableType);
                attributeLineV2.setVcFlag(vcFlag);
                // 只设置一次propertyGroupId，使用之前确定的值
                attributeLineV2.setPropertyGroupId(propertyGroupId);
                amazonAttributeLinesV2.add(attributeLineV2);
            }
        }

        if (ObjUtil.isNotEmpty(goodsHead.getFnSku())) {
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            // attributeLineV2.setHeadId(Long.valueOf(goodsHead.getId()));
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(categoryId);
            attributeLineV2.setProductType(productType);
            attributeLineV2.setPropNodePath(AmazonAttributeEnum.FN_SKU.getInfoV2(vcFlag));
            attributeLineV2.setTableName(AmazonAttributeEnum.FN_SKU.getInfoV2(vcFlag));
            attributeLineV2.setTableValue(goodsHead.getFnSku());
            attributeLineV2.setTableType(4);
            attributeLineV2.setVcFlag(vcFlag);
            attributeLineV2.setPropertyGroupId(AttributeGroupIdGenerator.generateDefaultGroupId());
            amazonAttributeLinesV2.add(attributeLineV2);
        }

        return amazonAttributeLinesV2;
    }



    public Map<String, String> convertAttributeMap(JSONObject attributes,String keyPrefix) {
        if (ObjectUtil.isEmpty(attributes)){
            return new HashMap<>();
        }
        Map<String, String> attributeMap = new HashMap<>();
        for (String key : attributes.keySet()) {
            String keyCode = StrUtil.isBlank(keyPrefix) ? key : keyPrefix +"."+ key;
            if (attributes.get(key) instanceof JSONObject) {
                Map<String, String> map = convertAttributeMap(attributes.getJSONObject(key),keyCode);
                attributeMap.putAll(map);
                continue;
            }
            if (attributes.get(key) instanceof JSONArray){
                JSONArray jsonArray = attributes.getJSONArray(key);
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object o = jsonArray.get(i);
                    if (!(o instanceof JSONObject)) {
                        continue;
                    }
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    Map<String, String> map = convertAttributeMap(jsonObject, keyCode);
                    attributeMap.putAll(map);
                }
                continue;
            }
            attributeMap.put(keyCode, attributes.getString(key));
        }
        return attributeMap;
    }

    /**
     * 带属性组ID的多值属性解析方法
     * 能够为每个属性值分配对应的组标识，解决重复路径问题
     * 
     * @param attributes JSON对象
     * @param keyPrefix 键前缀
     * @return 属性名到带组ID的值列表的映射
     */
    public Map<String, List<AttributeWithGroupInfo>> convertAttributeMapMultiWithGroupId(JSONObject attributes, String keyPrefix) {
        return convertAttributeMapMultiWithGroupId(attributes, keyPrefix, AttributeGroupIdGenerator.generateDefaultGroupId());
    }
    
    /**
     * 带属性组ID的多值属性解析方法（内部递归方法）
     * 
     * @param attributes JSON对象
     * @param keyPrefix 键前缀
     * @param currentGroupId 当前组ID
     * @return 属性名到带组ID的值列表的映射
     */
    private Map<String, List<AttributeWithGroupInfo>> convertAttributeMapMultiWithGroupId(JSONObject attributes, String keyPrefix, String currentGroupId) {
        if (ObjectUtil.isEmpty(attributes)) {
            return new HashMap<>();
        }
        
        Map<String, List<AttributeWithGroupInfo>> attributeMultiMap = new HashMap<>();
        
        for (String key : attributes.keySet()) {
            String keyCode = StrUtil.isBlank(keyPrefix) ? key : keyPrefix + "." + key;
            Object value = attributes.get(key);
            
            // 处理JSON对象
            if (value instanceof JSONObject) {
                Map<String, List<AttributeWithGroupInfo>> nestedMap = convertAttributeMapMultiWithGroupId(
                    attributes.getJSONObject(key), 
                    keyCode, 
                    currentGroupId
                );
                mergeMultiMapsWithGroup(attributeMultiMap, nestedMap);
                continue;
            }
            
            // 处理JSON数组
            if (value instanceof JSONArray) {
                JSONArray jsonArray = attributes.getJSONArray(key);
                
                // 处理数组中的基本类型值
                boolean hasDirectValues = false;
                List<AttributeWithGroupInfo> directValues = new ArrayList<>();
                
                // 处理数组中的所有元素
                for (int i = 0; i < jsonArray.size(); i++) {
                    Object arrayItem = jsonArray.get(i);
                    
                    // 如果是JSON对象，解析其内部属性，为每个数组元素生成独立的组ID
                    if (arrayItem instanceof JSONObject) {
                        // 为当前数组元素生成组ID
                        String arrayGroupId = AttributeGroupIdGenerator.generateNestedArrayGroupId(
                            currentGroupId, 
                            key, 
                            i
                        );
                        
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        Map<String, List<AttributeWithGroupInfo>> objMap = convertAttributeMapMultiWithGroupId(
                            jsonObject, 
                            keyCode, 
                            arrayGroupId
                        );
                        mergeMultiMapsWithGroup(attributeMultiMap, objMap);
                    } 
                    // 处理基本类型值（字符串、数字等）
                    else if (arrayItem != null) {
                        hasDirectValues = true;
                        // 对于基本类型的数组元素，也为每个元素生成组ID
                        String arrayGroupId = AttributeGroupIdGenerator.generateNestedArrayGroupId(
                            currentGroupId, 
                            key, 
                            i
                        );
                        directValues.add(new AttributeWithGroupInfo(String.valueOf(arrayItem), arrayGroupId));
                    }
                }
                
                // 如果数组包含直接值，将其添加到map中
                if (hasDirectValues) {
                    attributeMultiMap.put(keyCode, directValues);
                }
                
                continue;
            }
            
            // 处理基本类型值
            if (value != null) {
                List<AttributeWithGroupInfo> valueList = new ArrayList<>();
                valueList.add(new AttributeWithGroupInfo(attributes.getString(key), currentGroupId));
                attributeMultiMap.put(keyCode, valueList);
            }
        }
        
        return attributeMultiMap;
    }
    
    /**
     * 合并两个带组ID的多值Map
     */
    private void mergeMultiMapsWithGroup(Map<String, List<AttributeWithGroupInfo>> targetMap, Map<String, List<AttributeWithGroupInfo>> sourceMap) {
        for (Map.Entry<String, List<AttributeWithGroupInfo>> entry : sourceMap.entrySet()) {
            if (targetMap.containsKey(entry.getKey())) {
                // 如果目标Map已包含该键，将源Map中的值添加到已有列表
                targetMap.get(entry.getKey()).addAll(entry.getValue());
            } else {
                // 否则，直接添加新键值对
                targetMap.put(entry.getKey(), entry.getValue());
            }
        }
    }
    
    /**
     * 合并两个多值Map
     */
    private void mergeMultiMaps(Map<String, List<String>> targetMap, Map<String, List<String>> sourceMap) {
        for (Map.Entry<String, List<String>> entry : sourceMap.entrySet()) {
            if (targetMap.containsKey(entry.getKey())) {
                // 如果目标Map已包含该键，将源Map中的值添加到已有列表
                targetMap.get(entry.getKey()).addAll(entry.getValue());
            } else {
                // 否则，直接添加新键值对
                targetMap.put(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 处理多值属性，生成多个ListingAmazonAttributeLineV2对象
     * 
     * @param goodsHead 商品头信息
     * @param categoryId 类目ID
     * @param productType 产品类型
     * @param propNodePath 属性节点路径
     * @param fieldName 字段名称
     * @param values 多个属性值
     * @param tableType 表类型
     * @param vcFlag VC标志
     * @param result 结果列表
     */
    private void handleMultiValueAttribute(
            GoodsHead goodsHead, 
            Integer categoryId, 
            String productType, 
            String propNodePath, 
            String fieldName, 
            List<String> values, 
            Integer tableType, 
            String vcFlag, 
            List<ListingAmazonAttributeLineV2> result) {
        
        // 如果值为空，不处理
        if (CollectionUtil.isEmpty(values)) {
            return;
        }
        
        // 特殊处理：required_product_compliance_certificate.value
        if (ObjUtil.equals("required_product_compliance_certificate.value", propNodePath)) {
            // 创建单个属性行，使用第一个值进行特殊逻辑处理
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(categoryId);
            attributeLineV2.setProductType(productType);
            attributeLineV2.setPropNodePath(propNodePath);
            attributeLineV2.setTableName(fieldName);
            
            String value = values.get(0);
            if (StrUtil.isNotEmpty(value) && value.contains("CARB")) {
                attributeLineV2.setTableValue("California Air Review Board (CARB)");
            } else {
                attributeLineV2.setTableValue("Not Applicable");
            }
            
            attributeLineV2.setTableType(tableType);
            attributeLineV2.setVcFlag(vcFlag);
            attributeLineV2.setPropertyGroupId(AttributeGroupIdGenerator.generateDefaultGroupId());
            result.add(attributeLineV2);
            return;
        }
        
        // 对于普通多值属性，为每个值创建一个属性行
        for (int i = 0; i < values.size(); i++) {
            String value = values.get(i);
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(categoryId);
            attributeLineV2.setProductType(productType);
            attributeLineV2.setPropNodePath(propNodePath);
            attributeLineV2.setTableName(fieldName);
            attributeLineV2.setTableValue(value);
            attributeLineV2.setTableType(tableType);
            attributeLineV2.setVcFlag(vcFlag);
            // 为多值属性生成组ID，使用属性路径和索引
            attributeLineV2.setPropertyGroupId(AttributeGroupIdGenerator.generateArrayGroupId(propNodePath, i));
            result.add(attributeLineV2);
        }
    }
    
    /**
     * 处理带属性组ID的多值属性，生成多个ListingAmazonAttributeLineV2对象
     * 
     * @param goodsHead 商品头信息
     * @param categoryId 类目ID
     * @param productType 产品类型
     * @param propNodePath 属性节点路径
     * @param fieldName 字段名称
     * @param attributeWithGroupInfos 带组ID的属性值列表
     * @param tableType 表类型
     * @param vcFlag VC标志
     * @param result 结果列表
     */
    private void handleMultiValueAttributeWithGroupId(
            GoodsHead goodsHead, 
            Integer categoryId, 
            String productType, 
            String propNodePath, 
            String fieldName, 
            List<AttributeWithGroupInfo> attributeWithGroupInfos, 
            Integer tableType, 
            String vcFlag, 
            List<ListingAmazonAttributeLineV2> result) {
        
        // 如果值为空，不处理
        if (CollectionUtil.isEmpty(attributeWithGroupInfos)) {
            return;
        }
        
        // 特殊处理：required_product_compliance_certificate.value
        if (ObjUtil.equals("required_product_compliance_certificate.value", propNodePath)) {
            // 创建单个属性行，使用第一个值进行特殊逻辑处理
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(categoryId);
            attributeLineV2.setProductType(productType);
            attributeLineV2.setPropNodePath(propNodePath);
            attributeLineV2.setTableName(fieldName);
            
            AttributeWithGroupInfo firstAttr = attributeWithGroupInfos.get(0);
            String value = firstAttr.getValue();
            if (StrUtil.isNotEmpty(value) && value.contains("CARB")) {
                attributeLineV2.setTableValue("California Air Review Board (CARB)");
            } else {
                attributeLineV2.setTableValue("Not Applicable");
            }
            
            attributeLineV2.setTableType(tableType);
            attributeLineV2.setVcFlag(vcFlag);
            attributeLineV2.setPropertyGroupId(firstAttr.getGroupId());
            result.add(attributeLineV2);
            return;
        }
        
        // 对于普通多值属性，为每个值创建一个属性行
        for (AttributeWithGroupInfo attributeWithGroupInfo : attributeWithGroupInfos) {
            ListingAmazonAttributeLineV2 attributeLineV2 = new ListingAmazonAttributeLineV2();
            attributeLineV2.setPdmGoodsCode(goodsHead.getPdmGoodsCode());
            attributeLineV2.setCategoryId(categoryId);
            attributeLineV2.setProductType(productType);
            attributeLineV2.setPropNodePath(propNodePath);
            attributeLineV2.setTableName(fieldName);
            attributeLineV2.setTableValue(attributeWithGroupInfo.getValue());
            attributeLineV2.setTableType(tableType);
            attributeLineV2.setVcFlag(vcFlag);
            // 使用从JSON解析过程中生成的组ID
            attributeLineV2.setPropertyGroupId(attributeWithGroupInfo.getGroupId());
            result.add(attributeLineV2);
        }
    }
}