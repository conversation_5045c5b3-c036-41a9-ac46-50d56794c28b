package promotion;

import cn.hutool.json.JSONUtil;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealAsin;
import com.suncent.smc.persistence.promotion.domain.entity.AmBestDealRecord;
import com.suncent.smc.persistence.promotion.service.IAmBestDealAsinService;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.provider.biz.promotion.AmazonVcPromotionBiz;
import com.suncent.smc.provider.biz.promotion.dto.AmazonVcPromotionDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 亚马逊VC促销活动业务测试类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class AmazonVcPromotionBizTest {

    @Autowired
    private AmazonVcPromotionBiz amazonVcPromotionBiz;

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    @Autowired
    private IAmBestDealAsinService amBestDealAsinService;

    /**
     * 测试构建促销活动JSON
     */
    @Test
    public void testBuildPromotionJson() {
        // 使用一个测试的promotion_id
        String testPromotionId = "01834c8e-caa6-476d-8baa-90f44c6057d7";

        try {
            // 首先创建测试数据
            createTestData(testPromotionId);

            // 测试构建JSON
            String jsonResult = amazonVcPromotionBiz.buildPromotionJson(testPromotionId);

            log.info("构建的促销活动JSON:");
            log.info(jsonResult);

            // 验证JSON格式
            AmazonVcPromotionDTO dto = JSONUtil.toBean(jsonResult, AmazonVcPromotionDTO.class);
            assert dto != null;
            assert dto.getPromotions() != null && !dto.getPromotions().isEmpty();

            AmazonVcPromotionDTO.Promotion promotion = dto.getPromotions().get(0);
            assert promotion.getLatest() != null;
            assert testPromotionId.equals(promotion.getLatest().getPromotionId());

            log.info("测试通过：成功构建促销活动JSON");

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    /**
     * 测试数据验证功能
     */
    @Test
    public void testValidatePromotionData() {
        String testPromotionId = "TEST_PROMOTION_002";

        try {
            // 创建测试数据
            createTestData(testPromotionId);

            // 测试数据验证
            AjaxResult result = amazonVcPromotionBiz.validatePromotionData(testPromotionId);

            log.info("验证结果: {}", JSONUtil.toJsonPrettyStr(result));

            assert result.isSuccess();

            log.info("测试通过：数据验证功能正常");

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    /**
     * 测试获取促销活动摘要
     */
    @Test
    public void testGetPromotionSummary() {
        String testPromotionId = "TEST_PROMOTION_003";

        try {
            // 创建测试数据
            createTestData(testPromotionId);

            // 测试获取摘要
            Map<String, Object> summary = amazonVcPromotionBiz.getPromotionSummary(testPromotionId);

            log.info("促销活动摘要: {}", JSONUtil.toJsonPrettyStr(summary));

            assert summary != null;
            assert !summary.containsKey("error");
            assert testPromotionId.equals(summary.get("promotionId"));

            log.info("测试通过：获取促销活动摘要功能正常");

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    /**
     * 测试异常情况处理
     */
    @Test
    public void testExceptionHandling() {
        try {
            // 测试空的promotion_id
            try {
                amazonVcPromotionBiz.buildPromotionJson("");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                log.info("正确处理了空promotion_id异常: {}", e.getMessage());
            }

            // 测试不存在的promotion_id
            try {
                amazonVcPromotionBiz.buildPromotionJson("NON_EXISTENT_PROMOTION");
                assert false : "应该抛出异常";
            } catch (Exception e) {
                log.info("正确处理了不存在的promotion_id异常: {}", e.getMessage());
            }

            log.info("测试通过：异常处理功能正常");

        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    /**
     * 创建测试数据
     */
    private void createTestData(String promotionId) {
        // 创建促销记录
        AmBestDealRecord record = new AmBestDealRecord();
        record.setPromotionId(promotionId);
        record.setPromotionName("测试促销活动 - " + promotionId);
        record.setPublishType(5); // VCDF
        record.setSite("US");
        record.setStatus("DRAFT");
        record.setDealType(1); // BD
        record.setEventType(1); // 自定义日期
        record.setStartDateUtc("2024-12-20T00:00:00.000Z");
        record.setEndDateUtc("2024-12-25T23:59:59.000Z");
        record.setCreatedDateTime("2024-12-19T10:00:00.000Z");
        record.setLastUpdateDateTime("2024-12-19T10:00:00.000Z");
        record.setDelFlag(0);

        // 保存促销记录
        amBestDealRecordService.insertAmBestDealRecord(record);

        // 创建ASIN记录
        AmBestDealAsin asin1 = new AmBestDealAsin();
        asin1.setRefBestDealId(record.getId());
        asin1.setPromotionId(promotionId);
        asin1.setPlatformGoodsId("B08TEST001");
        asin1.setPlatformGoodsCode("TEST-SKU-001");
        asin1.setPdmGoodsCode("PDM-001");
        asin1.setStandardPrice(new BigDecimal("29.99"));
        asin1.setReferencePrice(new BigDecimal("29.99"));
        asin1.setExpectedDemand(100);
        asin1.setCommittedUnits(50);
        asin1.setLowestDiscount(15);
        asin1.setActualDiscount(20);
        asin1.setDealPrice(new BigDecimal("23.99"));
        asin1.setPerUnitFunding(new BigDecimal("6.00"));
        asin1.setDelFlag(0);

        AmBestDealAsin asin2 = new AmBestDealAsin();
        asin2.setRefBestDealId(record.getId());
        asin2.setPromotionId(promotionId);
        asin2.setPlatformGoodsId("B08TEST002");
        asin2.setPlatformGoodsCode("TEST-SKU-002");
        asin2.setPdmGoodsCode("PDM-002");
        asin2.setStandardPrice(new BigDecimal("49.99"));
        asin2.setReferencePrice(new BigDecimal("49.99"));
        asin2.setExpectedDemand(80);
        asin2.setCommittedUnits(40);
        asin2.setLowestDiscount(10);
        asin2.setActualDiscount(15);
        asin2.setDealPrice(new BigDecimal("42.49"));
        asin2.setPerUnitFunding(new BigDecimal("7.50"));
        asin2.setDelFlag(0);

        // 保存ASIN记录
        amBestDealAsinService.insertAmBestDealAsin(asin1);
        amBestDealAsinService.insertAmBestDealAsin(asin2);

        log.info("创建测试数据完成，促销ID: {}, 记录ID: {}", promotionId, record.getId());
    }

    /**
     * 测试现有数据
     * 如果数据库中已有真实数据，可以使用这个方法测试
     */
    @Test
    public void testWithExistingData() {
        try {
            // 查询数据库中的第一条记录进行测试
            AmBestDealRecord record = new AmBestDealRecord();
            List<AmBestDealRecord> records = amBestDealRecordService.selectAmBestDealRecordList(record);

            if (records != null && !records.isEmpty()) {
                String promotionId = records.get(0).getPromotionId();
                log.info("使用现有数据测试，促销ID: {}", promotionId);

                // 测试构建JSON
                String jsonResult = amazonVcPromotionBiz.buildPromotionJson(promotionId);
                log.info("现有数据构建的JSON: {}", jsonResult);

                // 测试验证
                AjaxResult validateResult = amazonVcPromotionBiz.validatePromotionData(promotionId);
                log.info("现有数据验证结果: {}", JSONUtil.toJsonPrettyStr(validateResult));

                // 测试摘要
                Map<String, Object> summary = amazonVcPromotionBiz.getPromotionSummary(promotionId);
                log.info("现有数据摘要: {}", JSONUtil.toJsonPrettyStr(summary));

            } else {
                log.info("数据库中没有促销记录，跳过现有数据测试");
            }

        } catch (Exception e) {
            log.error("现有数据测试失败", e);
            // 不抛出异常，因为可能没有现有数据
        }
    }

    /**
     * 测试构建简化的促销活动JSON
     */
    @Test
    public void testBuildSimplePromotionJson() {
        // 使用一个测试的promotion_id
        String testPromotionId = "TEST_PROMOTION_001";

        try {
            // 先创建测试数据
            createTestDataForSimpleJson(testPromotionId);

            // 测试构建简化JSON
            String jsonResult = amazonVcPromotionBiz.buildSimplePromotionJson(testPromotionId);

            log.info("构建的简化促销活动JSON:");
            log.info(jsonResult);

            // 验证JSON结构
            Map<String, Object> resultMap = JSONUtil.toBean(jsonResult, Map.class);

            // 验证products数组
            assert resultMap.containsKey("products");
            List<Map<String, Object>> products = (List<Map<String, Object>>) resultMap.get("products");
            assert !products.isEmpty();

            Map<String, Object> firstProduct = products.get(0);
            assert firstProduct.containsKey("asin");
            assert firstProduct.containsKey("sku");
            assert firstProduct.containsKey("promotionQuantity");
            assert firstProduct.containsKey("perUnitFunding");
            assert firstProduct.containsKey("promotionPrice");

            // 验证promotion对象
            assert resultMap.containsKey("promotion");
            Map<String, Object> promotion = (Map<String, Object>) resultMap.get("promotion");
            assert promotion.containsKey("internalDescription");
            assert promotion.containsKey("featuredAsin");
            assert promotion.containsKey("marketplaceId");
            assert promotion.containsKey("offeringName");
            assert promotion.containsKey("schedule");
            assert promotion.containsKey("owner");

            log.info("简化促销活动JSON构建测试通过");

        } catch (Exception e) {
            log.error("简化促销活动JSON构建测试失败", e);
            throw e;
        }
    }

    /**
     * 创建简化JSON测试数据
     */
    private void createTestDataForSimpleJson(String promotionId) {
        log.info("开始创建简化JSON测试数据，促销ID: {}", promotionId);

        // 创建促销记录
        AmBestDealRecord record = new AmBestDealRecord();
        record.setPromotionId(promotionId);
        record.setPromotionName("Test Simple Promotion");
        record.setPublishType(6); // VCPO
        record.setSite("US");
        record.setStatus("APPROVED");
        record.setDealType(1); // BD
        record.setEventType(1); // 自定义日期
        record.setStartDateUtc("2025-08-01T00:00:00");
        record.setEndDateUtc("2025-08-02T23:45:00");
        record.setDelFlag(0);

        // 保存促销记录
        amBestDealRecordService.insertAmBestDealRecord(record);

        // 创建ASIN记录
        AmBestDealAsin asin1 = new AmBestDealAsin();
        asin1.setRefBestDealId(record.getId());
        asin1.setPromotionId(promotionId);
        asin1.setPlatformGoodsId("B0CYBV89NM");
        asin1.setPlatformGoodsCode("B0CYBV89NM");
        asin1.setPdmGoodsCode("PDM-001");
        asin1.setStandardPrice(new BigDecimal("96.73"));
        asin1.setReferencePrice(new BigDecimal("96.73"));
        asin1.setExpectedDemand(100);
        asin1.setCommittedUnits(100);
        asin1.setLowestDiscount(10);
        asin1.setActualDiscount(15);
        asin1.setDealPrice(new BigDecimal("83.57"));
        asin1.setPerUnitFunding(new BigDecimal("13.16"));
        asin1.setDelFlag(0);

        // 保存ASIN记录
        amBestDealAsinService.insertAmBestDealAsin(asin1);

        log.info("创建简化JSON测试数据完成，促销ID: {}, 记录ID: {}", promotionId, record.getId());
    }

    /**
     * 测试现有数据的简化JSON构建
     */
    @Test
    public void testBuildSimplePromotionJsonWithExistingData() {
        try {
            // 查询数据库中的第一条促销记录
            AmBestDealRecord queryRecord = new AmBestDealRecord();
            queryRecord.setDelFlag(0);
            List<AmBestDealRecord> records = amBestDealRecordService.selectAmBestDealRecordList(queryRecord);

            if (!records.isEmpty()) {
                AmBestDealRecord record = records.get(0);
                String promotionId = record.getPromotionId();

                log.info("使用现有数据测试简化JSON构建，促销ID: {}", promotionId);

                // 构建简化JSON
                String jsonResult = amazonVcPromotionBiz.buildSimplePromotionJson(promotionId);

                log.info("现有数据构建的简化促销活动JSON:");
                log.info(jsonResult);

                // 验证JSON结构
                Map<String, Object> resultMap = JSONUtil.toBean(jsonResult, Map.class);
                assert resultMap.containsKey("products");
                assert resultMap.containsKey("promotion");

                log.info("现有数据简化JSON构建测试通过");

            } else {
                log.info("数据库中没有促销记录，跳过现有数据简化JSON测试");
            }

        } catch (Exception e) {
            log.error("现有数据简化JSON测试失败", e);
            // 不抛出异常，因为可能没有现有数据
        }
    }
}
